<?php
/**
 * اختبار بسيط لنظام الكوبونات
 * Simple Coupon System Test
 */

echo "🧪 اختبار بسيط لنظام الكوبونات\n";
echo "===============================\n\n";

// إعداد قاعدة البيانات
try {
    // تضمين ملف إعدادات قاعدة البيانات
    require_once __DIR__ . '/../config/database.php';

    echo "✅ تم تحميل إعدادات قاعدة البيانات\n";

    // استخدام الاتصال الموجود
    if (!isset($pdo)) {
        $pdo = getDBConnection();
    }
    
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح\n\n";
    
    // 1. فحص وجود جدول الكوبونات
    echo "📋 فحص جدول الكوبونات...\n";
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'coupons'");
        if ($stmt->rowCount() > 0) {
            echo "✅ جدول الكوبونات موجود\n";
            
            // فحص الأعمدة
            $stmt = $pdo->query("DESCRIBE coupons");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            echo "📊 الأعمدة الموجودة: " . implode(', ', $columns) . "\n";
            
            // عدد الكوبونات
            $stmt = $pdo->query("SELECT COUNT(*) FROM coupons");
            $count = $stmt->fetchColumn();
            echo "📊 عدد الكوبونات: $count\n";
            
        } else {
            echo "❌ جدول الكوبونات غير موجود\n";
            echo "💡 يجب تشغيل: database/quick_test_setup.sql\n";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في فحص الجدول: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 2. اختبار إنشاء كوبون بسيط
    echo "📋 اختبار إنشاء كوبون...\n";
    try {
        // التحقق من وجود جدول الكوبونات أولاً
        $stmt = $pdo->query("SHOW TABLES LIKE 'coupons'");
        if ($stmt->rowCount() > 0) {
            
            $sql = "INSERT INTO coupons (uuid, code, name, description, type, value, minimum_amount, valid_from, valid_until, is_active) 
                    VALUES (UUID(), ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([
                'SIMPLE2025',
                'كوبون بسيط 2025',
                'كوبون اختبار بسيط',
                'percentage',
                15.00,
                30.00,
                '2025-01-01 00:00:00',
                '2025-12-31 23:59:59',
                1
            ]);
            
            if ($result) {
                $couponId = $pdo->lastInsertId();
                echo "✅ تم إنشاء الكوبون بنجاح - المعرف: $couponId\n";
            } else {
                echo "❌ فشل في إنشاء الكوبون\n";
            }
            
        } else {
            echo "⚠️ تخطي الاختبار - الجدول غير موجود\n";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في إنشاء الكوبون: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 3. عرض جميع الكوبونات
    echo "📋 عرض الكوبونات الموجودة...\n";
    try {
        $stmt = $pdo->query("SELECT id, code, name, type, value, is_active FROM coupons ORDER BY id DESC LIMIT 5");
        $coupons = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($coupons) > 0) {
            echo "📊 الكوبونات الموجودة:\n";
            foreach ($coupons as $coupon) {
                $status = $coupon['is_active'] ? '🟢 مفعل' : '🔴 معطل';
                echo "  - {$coupon['id']}: {$coupon['code']} - {$coupon['name']} ({$coupon['type']}: {$coupon['value']}) $status\n";
            }
        } else {
            echo "⚠️ لا توجد كوبونات\n";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في عرض الكوبونات: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 4. ملخص النتائج
    echo "📊 ملخص الاختبار:\n";
    echo "================\n";
    echo "✅ الاتصال بقاعدة البيانات: نجح\n";
    
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM coupons");
        $count = $stmt->fetchColumn();
        echo "✅ إجمالي الكوبونات: $count\n";
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM coupons WHERE is_active = 1");
        $activeCount = $stmt->fetchColumn();
        echo "✅ الكوبونات المفعلة: $activeCount\n";
        
    } catch (Exception $e) {
        echo "❌ خطأ في الملخص: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "\n";
    echo "💡 تأكد من:\n";
    echo "   - تشغيل خادم MySQL\n";
    echo "   - صحة بيانات الاتصال\n";
    echo "   - وجود قاعدة البيانات c7c_wolves7c\n";
}

echo "\n✅ انتهى الاختبار البسيط\n";
?>
