<?php
/**
 * إعداد وتشغيل اختبارات نظام الكوبونات
 * Setup and Run Coupon System Tests
 */

echo "🚀 إعداد وتشغيل نظام الكوبونات\n";
echo "===============================\n\n";

// تضمين إعدادات قاعدة البيانات
require_once __DIR__ . '/../config/database.php';

try {
    echo "📋 الخطوة 1: إنشاء الجداول...\n";
    
    // قراءة وتنفيذ ملف SQL
    $sqlFile = __DIR__ . '/../database/quick_test_setup.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("ملف SQL غير موجود: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    
    // تقسيم الاستعلامات
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($statement);
        } catch (Exception $e) {
            echo "⚠️ تحذير في الاستعلام: " . $e->getMessage() . "\n";
        }
    }
    
    echo "✅ تم إنشاء الجداول بنجاح\n\n";
    
    // فحص الجداول المُنشأة
    echo "📋 الخطوة 2: فحص الجداول...\n";
    
    $tables = ['coupons', 'coupon_usage'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                echo "✅ جدول $table: موجود\n";
                
                // عرض الأعمدة
                $stmt = $pdo->query("DESCRIBE $table");
                $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                echo "   الأعمدة: " . implode(', ', $columns) . "\n";
            } else {
                echo "❌ جدول $table: غير موجود\n";
            }
        } catch (Exception $e) {
            echo "❌ خطأ في فحص جدول $table: " . $e->getMessage() . "\n";
        }
    }
    echo "\n";
    
    // إدراج بيانات تجريبية
    echo "📋 الخطوة 3: إدراج بيانات تجريبية...\n";
    
    try {
        // فحص وجود الكوبونات
        $stmt = $pdo->query("SELECT COUNT(*) FROM coupons");
        $count = $stmt->fetchColumn();
        
        if ($count == 0) {
            // إدراج كوبونات تجريبية
            $coupons = [
                [
                    'uuid' => 'test-uuid-1',
                    'code' => 'TEST2025',
                    'name' => 'كوبون اختبار 2025',
                    'description' => 'كوبون تجريبي للاختبار',
                    'type' => 'percentage',
                    'value' => 10.00,
                    'minimum_amount' => 50.00,
                    'maximum_discount' => 100.00,
                    'usage_limit' => 100,
                    'usage_limit_per_user' => 1,
                    'used_count' => 0,
                    'valid_from' => '2025-01-01 00:00:00',
                    'valid_until' => '2025-12-31 23:59:59',
                    'is_active' => 1
                ],
                [
                    'uuid' => 'test-uuid-2',
                    'code' => 'FIXED50',
                    'name' => 'خصم ثابت 50',
                    'description' => 'كوبون خصم ثابت 50 ريال',
                    'type' => 'fixed',
                    'value' => 50.00,
                    'minimum_amount' => 100.00,
                    'maximum_discount' => null,
                    'usage_limit' => 50,
                    'usage_limit_per_user' => 1,
                    'used_count' => 0,
                    'valid_from' => '2025-01-01 00:00:00',
                    'valid_until' => '2025-12-31 23:59:59',
                    'is_active' => 1
                ]
            ];
            
            $sql = "INSERT INTO coupons (uuid, code, name, description, type, value, minimum_amount, maximum_discount, usage_limit, usage_limit_per_user, used_count, valid_from, valid_until, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $pdo->prepare($sql);
            
            foreach ($coupons as $coupon) {
                $stmt->execute(array_values($coupon));
            }
            
            echo "✅ تم إدراج " . count($coupons) . " كوبون تجريبي\n";
        } else {
            echo "✅ يوجد $count كوبون في قاعدة البيانات\n";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في إدراج البيانات: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // تشغيل الاختبار البسيط
    echo "📋 الخطوة 4: تشغيل الاختبار...\n";
    echo str_repeat("=", 50) . "\n";
    
    include __DIR__ . '/simple_coupon_test.php';
    
} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
}

echo "\n🎉 انتهى الإعداد والاختبار\n";
?>
