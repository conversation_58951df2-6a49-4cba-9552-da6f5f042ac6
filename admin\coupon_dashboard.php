<?php
/**
 * لوحة تحكم الكوبونات - Coupon Dashboard
 * إدارة شاملة لنظام الكوبونات
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/layout.php';
require_once __DIR__ . '/../includes/CouponManager.php';
require_once __DIR__ . '/../includes/CouponReportingSystem.php';

// التحقق من الصلاحيات
require_roles(['admin', 'super_admin']);

$pdo = getDBConnection();
$couponManager = new CouponManager($pdo);
$reportingSystem = new CouponReportingSystem($pdo);

// الحصول على الإحصائيات
$stats = [
    'total_coupons' => 0,
    'active_coupons' => 0,
    'expired_coupons' => 0,
    'total_usage' => 0,
    'total_discount' => 0,
    'total_revenue' => 0
];

try {
    // إجمالي الكوبونات
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM coupons");
    $stats['total_coupons'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // الكوبونات النشطة
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM coupons WHERE is_active = 1");
    $stats['active_coupons'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // الكوبونات المنتهية
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM coupons WHERE expires_at < NOW()");
    $stats['expired_coupons'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // إجمالي الاستخدام
    $stmt = $pdo->query("SELECT SUM(usage_count) as total FROM coupons");
    $stats['total_usage'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;
    
    // إجمالي الخصم
    $stmt = $pdo->query("SELECT SUM(total_discount_amount) as total FROM coupons");
    $stats['total_discount'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;
    
} catch (Exception $e) {
    // معالجة الأخطاء
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم الكوبونات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f7fa; }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header { margin-bottom: 30px; }
        .header h1 { color: #2c3e50; font-size: 28px; margin-bottom: 10px; }
        .header p { color: #7f8c8d; font-size: 14px; }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-right: 4px solid #3498db;
        }
        
        .stat-card.active { border-right-color: #27ae60; }
        .stat-card.expired { border-right-color: #e74c3c; }
        .stat-card.usage { border-right-color: #f39c12; }
        
        .stat-label { color: #7f8c8d; font-size: 12px; text-transform: uppercase; margin-bottom: 8px; }
        .stat-value { font-size: 32px; font-weight: bold; color: #2c3e50; }
        .stat-change { font-size: 12px; color: #27ae60; margin-top: 8px; }
        
        .actions-bar {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s;
        }
        
        .btn-primary { background: #3498db; color: white; }
        .btn-primary:hover { background: #2980b9; }
        .btn-success { background: #27ae60; color: white; }
        .btn-success:hover { background: #229954; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-warning:hover { background: #d68910; }
        
        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }
        
        .quick-link {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .quick-link:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .quick-link i { font-size: 32px; color: #3498db; margin-bottom: 10px; }
        .quick-link-title { font-weight: bold; color: #2c3e50; margin-bottom: 5px; }
        .quick-link-desc { font-size: 12px; color: #7f8c8d; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-ticket-alt"></i> لوحة تحكم الكوبونات</h1>
            <p>إدارة شاملة لنظام الكوبونات والخصومات</p>
        </div>
        
        <!-- الإحصائيات -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-label">إجمالي الكوبونات</div>
                <div class="stat-value"><?php echo $stats['total_coupons']; ?></div>
                <div class="stat-change"><i class="fas fa-arrow-up"></i> جميع الكوبونات</div>
            </div>
            
            <div class="stat-card active">
                <div class="stat-label">الكوبونات النشطة</div>
                <div class="stat-value"><?php echo $stats['active_coupons']; ?></div>
                <div class="stat-change"><i class="fas fa-check-circle"></i> جاهزة للاستخدام</div>
            </div>
            
            <div class="stat-card expired">
                <div class="stat-label">الكوبونات المنتهية</div>
                <div class="stat-value"><?php echo $stats['expired_coupons']; ?></div>
                <div class="stat-change"><i class="fas fa-times-circle"></i> انتهت صلاحيتها</div>
            </div>
            
            <div class="stat-card usage">
                <div class="stat-label">إجمالي الاستخدام</div>
                <div class="stat-value"><?php echo $stats['total_usage']; ?></div>
                <div class="stat-change"><i class="fas fa-chart-line"></i> مرات الاستخدام</div>
            </div>
        </div>
        
        <!-- شريط الإجراءات -->
        <div class="actions-bar">
            <a href="coupons.php" class="btn btn-primary">
                <i class="fas fa-list"></i> إدارة الكوبونات
            </a>
            <a href="coupon_create.php" class="btn btn-success">
                <i class="fas fa-plus"></i> إنشاء كوبون جديد
            </a>
            <a href="coupon_reports.php" class="btn btn-warning">
                <i class="fas fa-chart-bar"></i> التقارير والإحصائيات
            </a>
            <a href="coupon_subscriptions.php" class="btn btn-primary">
                <i class="fas fa-link"></i> تكامل الاشتراكات
            </a>
        </div>
        
        <!-- الروابط السريعة -->
        <h2 style="margin-top: 40px; margin-bottom: 20px; color: #2c3e50;">
            <i class="fas fa-bolt"></i> الروابط السريعة
        </h2>
        <div class="quick-links">
            <div class="quick-link" onclick="window.location.href='coupons.php'">
                <i class="fas fa-ticket-alt"></i>
                <div class="quick-link-title">إدارة الكوبونات</div>
                <div class="quick-link-desc">عرض وتعديل وحذف الكوبونات</div>
            </div>
            
            <div class="quick-link" onclick="window.location.href='coupon_create.php'">
                <i class="fas fa-plus-circle"></i>
                <div class="quick-link-title">إنشاء كوبون</div>
                <div class="quick-link-desc">إضافة كوبون جديد للنظام</div>
            </div>
            
            <div class="quick-link" onclick="window.location.href='coupon_reports.php'">
                <i class="fas fa-chart-bar"></i>
                <div class="quick-link-title">التقارير</div>
                <div class="quick-link-desc">عرض التقارير والإحصائيات</div>
            </div>
            
            <div class="quick-link" onclick="window.location.href='coupon_subscriptions.php'">
                <i class="fas fa-link"></i>
                <div class="quick-link-title">تكامل الاشتراكات</div>
                <div class="quick-link-desc">إدارة الكوبونات مع الاشتراكات</div>
            </div>
        </div>
    </div>
</body>
</html>

