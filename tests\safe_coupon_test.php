<?php
/**
 * اختبار آمن لنظام الكوبونات
 * Safe Coupon System Test
 */

echo "🧪 اختبار آمن لنظام الكوبونات\n";
echo "===============================\n\n";

// إعداد قاعدة البيانات
try {
    // تضمين ملف إعدادات قاعدة البيانات
    require_once __DIR__ . '/../config/database.php';
    
    echo "✅ تم تحميل إعدادات قاعدة البيانات\n";
    echo "📊 المضيف: " . DB_HOST . "\n";
    echo "📊 قاعدة البيانات: " . DB_NAME . "\n";
    echo "📊 المستخدم: " . DB_USER . "\n\n";

    // استخدام الاتصال الموجود
    if (!isset($pdo)) {
        $pdo = getDBConnection();
    }
    
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح\n\n";
    
    // 1. تشغيل الإعداد الآمن
    echo "📋 تشغيل الإعداد الآمن...\n";
    try {
        $sqlFile = __DIR__ . '/../database/simple_safe_setup.sql';
        if (file_exists($sqlFile)) {
            $sql = file_get_contents($sqlFile);
            
            // تقسيم الاستعلامات
            $statements = array_filter(array_map('trim', explode(';', $sql)));
            
            $successCount = 0;
            $errorCount = 0;
            
            foreach ($statements as $statement) {
                if (!empty($statement) && !preg_match('/^--/', $statement)) {
                    try {
                        $result = $pdo->exec($statement);
                        $successCount++;
                        
                        // عرض نتائج SELECT
                        if (preg_match('/^SELECT/', trim($statement))) {
                            $stmt = $pdo->query($statement);
                            $result = $stmt->fetch(PDO::FETCH_ASSOC);
                            if ($result) {
                                foreach ($result as $key => $value) {
                                    echo "   📊 $key: $value\n";
                                }
                            }
                        }
                        
                    } catch (Exception $e) {
                        $errorCount++;
                        echo "⚠️ تحذير: " . $e->getMessage() . "\n";
                    }
                }
            }
            
            echo "✅ تم تنفيذ $successCount استعلام بنجاح\n";
            if ($errorCount > 0) {
                echo "⚠️ $errorCount تحذيرات\n";
            }
            
        } else {
            echo "❌ ملف SQL غير موجود: $sqlFile\n";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في تشغيل الإعداد: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 2. فحص الجداول
    echo "📋 فحص الجداول...\n";
    $tables = ['coupons', 'coupon_usage'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                echo "✅ جدول $table: موجود\n";
                
                // عدد السجلات
                $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
                $count = $stmt->fetchColumn();
                echo "   📊 عدد السجلات: $count\n";
                
            } else {
                echo "❌ جدول $table: غير موجود\n";
            }
        } catch (Exception $e) {
            echo "❌ خطأ في فحص جدول $table: " . $e->getMessage() . "\n";
        }
    }
    echo "\n";
    
    // 3. عرض الكوبونات
    echo "📋 عرض الكوبونات الموجودة...\n";
    try {
        $stmt = $pdo->query("SELECT id, code, name, type, value, is_active FROM coupons ORDER BY id");
        $coupons = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($coupons) > 0) {
            echo "✅ تم العثور على " . count($coupons) . " كوبون:\n";
            foreach ($coupons as $coupon) {
                $status = $coupon['is_active'] ? '🟢 مفعل' : '🔴 معطل';
                echo "  - {$coupon['id']}: {$coupon['code']} - {$coupon['name']}\n";
                echo "    النوع: {$coupon['type']}, القيمة: {$coupon['value']}, الحالة: $status\n";
            }
        } else {
            echo "⚠️ لا توجد كوبونات\n";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في عرض الكوبونات: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 4. اختبار إنشاء كوبون جديد
    echo "📋 اختبار إنشاء كوبون جديد...\n";
    try {
        $sql = "INSERT INTO coupons (uuid, code, name, description, type, value, minimum_amount, valid_from, valid_until, is_active) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            'new-test-uuid-' . time(),
            'NEWTEST' . date('Y'),
            'كوبون جديد ' . date('Y'),
            'كوبون تم إنشاؤه من الاختبار الآمن',
            'percentage',
            30.00,
            15.00,
            '2025-01-01 00:00:00',
            '2025-12-31 23:59:59',
            1
        ]);
        
        if ($result) {
            $couponId = $pdo->lastInsertId();
            echo "✅ تم إنشاء كوبون جديد بنجاح - المعرف: $couponId\n";
        } else {
            echo "❌ فشل في إنشاء الكوبون\n";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في إنشاء الكوبون: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 5. ملخص النتائج
    echo "📊 ملخص النتائج:\n";
    echo "================\n";
    
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM coupons");
        $totalCoupons = $stmt->fetchColumn();
        echo "✅ إجمالي الكوبونات: $totalCoupons\n";
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM coupons WHERE is_active = 1");
        $activeCoupons = $stmt->fetchColumn();
        echo "✅ الكوبونات المفعلة: $activeCoupons\n";
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM coupon_usage");
        $usageCount = $stmt->fetchColumn();
        echo "✅ سجلات الاستخدام: $usageCount\n";
        
        echo "\n🎉 نظام الكوبونات يعمل بشكل صحيح!\n";
        
    } catch (Exception $e) {
        echo "❌ خطأ في الملخص: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
    echo "\n💡 تأكد من:\n";
    echo "   - تشغيل خادم MySQL\n";
    echo "   - صحة بيانات الاتصال في config/database.php\n";
    echo "   - وجود قاعدة البيانات " . (defined('DB_NAME') ? DB_NAME : 'c7c_wolves7c') . "\n";
    echo "   - صلاحيات المستخدم للوصول لقاعدة البيانات\n";
}

echo "\n✅ انتهى الاختبار الآمن\n";
?>
