<?php
/**
 * تكامل الكوبونات مع نظام الولاء
 * Coupon Loyalty Integration
 */

class CouponLoyaltyIntegration {
    private $pdo;
    private $couponManager;
    
    public function __construct(PDO $pdo, CouponManager $couponManager) {
        $this->pdo = $pdo;
        $this->couponManager = $couponManager;
    }
    
    /**
     * منح نقاط الولاء عند استخدام الكوبون
     */
    public function awardLoyaltyPoints(int $couponId, int $userId, float $discountAmount): array {
        try {
            // الحصول على معلومات الكوبون والولاء
            $loyaltyMapping = $this->getLoyaltyMapping($couponId);
            
            if (!$loyaltyMapping) {
                return ['success' => true, 'points_awarded' => 0];
            }
            
            $pointsToAward = (int)$loyaltyMapping['loyalty_points_earned'];
            
            if ($pointsToAward <= 0) {
                return ['success' => true, 'points_awarded' => 0];
            }
            
            // إضافة النقاط
            $this->addLoyaltyPoints($userId, $pointsToAward, 'coupon_usage', "استخدام كوبون: {$couponId}");
            
            return [
                'success' => true,
                'points_awarded' => $pointsToAward,
                'message' => "تم منح {$pointsToAward} نقطة ولاء"
            ];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * استخدام نقاط الولاء للحصول على كوبون
     */
    public function redeemCouponWithLoyaltyPoints(int $couponId, int $userId): array {
        try {
            $coupon = $this->couponManager->getCoupon($couponId);
            if (!$coupon) {
                return ['success' => false, 'error' => 'الكوبون غير موجود'];
            }
            
            $loyaltyMapping = $this->getLoyaltyMapping($couponId);
            if (!$loyaltyMapping) {
                return ['success' => false, 'error' => 'هذا الكوبون لا يمكن استبداله بنقاط الولاء'];
            }
            
            $pointsRequired = (int)$loyaltyMapping['loyalty_points_required'];
            
            if ($pointsRequired <= 0) {
                return ['success' => false, 'error' => 'هذا الكوبون لا يتطلب نقاط ولاء'];
            }
            
            // التحقق من رصيد النقاط
            $userPoints = $this->getUserLoyaltyPoints($userId);
            
            if ($userPoints < $pointsRequired) {
                return [
                    'success' => false,
                    'error' => "رصيدك من نقاط الولاء ({$userPoints}) أقل من المطلوب ({$pointsRequired})"
                ];
            }
            
            // خصم النقاط
            $this->deductLoyaltyPoints($userId, $pointsRequired, 'coupon_redemption', "استبدال كوبون: {$couponId}");
            
            return [
                'success' => true,
                'coupon_code' => $coupon['code'],
                'points_used' => $pointsRequired,
                'remaining_points' => $userPoints - $pointsRequired,
                'message' => 'تم استبدال الكوبون بنقاط الولاء'
            ];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * إنشاء كوبون خاص بنقاط الولاء
     */
    public function createLoyaltyCoupon(array $couponData, array $loyaltyData): array {
        try {
            // إنشاء الكوبون
            $couponResult = $this->couponManager->createCoupon($couponData);
            
            if (!$couponResult['success']) {
                return $couponResult;
            }
            
            $couponId = $couponResult['id'];
            
            // ربط الكوبون بنقاط الولاء
            $sql = "INSERT INTO coupon_loyalty_mapping (
                coupon_id, loyalty_points_required, loyalty_points_earned
            ) VALUES (?, ?, ?)";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([
                $couponId,
                $loyaltyData['points_required'] ?? 0,
                $loyaltyData['points_earned'] ?? 0
            ]);
            
            return [
                'success' => true,
                'coupon_id' => $couponId,
                'message' => 'تم إنشاء كوبون الولاء بنجاح'
            ];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * الحصول على معلومات ربط الكوبون بالولاء
     */
    private function getLoyaltyMapping(int $couponId): ?array {
        $sql = "SELECT * FROM coupon_loyalty_mapping WHERE coupon_id = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$couponId]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }
    
    /**
     * إضافة نقاط الولاء
     */
    private function addLoyaltyPoints(int $userId, int $points, string $source, string $description): void {
        $sql = "INSERT INTO loyalty_points_ledger (
            player_id, source, delta, description, created_at
        ) VALUES (?, ?, ?, ?, NOW())";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$userId, $source, $points, $description]);
        
        // تحديث الرصيد
        $this->updateLoyaltyBalance($userId);
    }
    
    /**
     * خصم نقاط الولاء
     */
    private function deductLoyaltyPoints(int $userId, int $points, string $source, string $description): void {
        $sql = "INSERT INTO loyalty_points_ledger (
            player_id, source, delta, description, created_at
        ) VALUES (?, ?, ?, ?, NOW())";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$userId, $source, -$points, $description]);
        
        // تحديث الرصيد
        $this->updateLoyaltyBalance($userId);
    }
    
    /**
     * الحصول على رصيد نقاط الولاء للمستخدم
     */
    private function getUserLoyaltyPoints(int $userId): int {
        $sql = "SELECT COALESCE(SUM(delta), 0) as total FROM loyalty_points_ledger WHERE player_id = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$userId]);
        return (int)$stmt->fetch(PDO::FETCH_ASSOC)['total'];
    }
    
    /**
     * تحديث رصيد الولاء
     */
    private function updateLoyaltyBalance(int $userId): void {
        $balance = $this->getUserLoyaltyPoints($userId);
        
        $sql = "INSERT INTO loyalty_points (player_id, points_balance, updated_at)
                VALUES (?, ?, NOW())
                ON DUPLICATE KEY UPDATE 
                points_balance = ?, updated_at = NOW()";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$userId, $balance, $balance]);
    }
    
    /**
     * الحصول على إحصائيات الولاء للمستخدم
     */
    public function getUserLoyaltyStats(int $userId): array {
        try {
            $sql = "SELECT
                    COALESCE(SUM(CASE WHEN delta > 0 THEN delta ELSE 0 END), 0) as total_earned,
                    COALESCE(SUM(CASE WHEN delta < 0 THEN ABS(delta) ELSE 0 END), 0) as total_redeemed,
                    COALESCE(SUM(delta), 0) as current_balance
                    FROM loyalty_points_ledger WHERE player_id = ?";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$userId]);
            $stats = $stmt->fetch(PDO::FETCH_ASSOC);

            return [
                'success' => true,
                'total_earned' => (int)$stats['total_earned'],
                'total_redeemed' => (int)$stats['total_redeemed'],
                'current_balance' => (int)$stats['current_balance']
            ];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * حساب نقاط الولاء بناءً على قيمة الخصم
     * Calculate loyalty points based on discount value
     */
    public function calculateLoyaltyPointsFromDiscount(float $discountAmount, int $couponId = null): int {
        try {
            // الحصول على معدل التحويل من الإعدادات
            $pointsPerUnit = 1; // نقطة واحدة لكل وحدة عملة

            // حساب النقاط الأساسية
            $basePoints = (int)($discountAmount * $pointsPerUnit);

            // إذا كان هناك كوبون محدد، تحقق من المضاعفات
            if ($couponId) {
                $coupon = $this->couponManager->getCoupon($couponId);
                if ($coupon && isset($coupon['loyalty_multiplier'])) {
                    $basePoints = (int)($basePoints * $coupon['loyalty_multiplier']);
                }
            }

            return max(0, $basePoints);
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * الحصول على الكوبونات المتاحة للاستبدال بنقاط الولاء
     * Get available coupons for loyalty points redemption
     */
    public function getAvailableLoyaltyCoupons(int $userId, int $limit = 10): array {
        try {
            $userPoints = $this->getUserLoyaltyPoints($userId);

            $sql = "SELECT c.*, clm.loyalty_points_required, clm.loyalty_points_earned
                    FROM coupons c
                    INNER JOIN coupon_loyalty_mapping clm ON c.id = clm.coupon_id
                    WHERE c.is_active = 1
                    AND (c.expires_at IS NULL OR c.expires_at > NOW())
                    AND clm.loyalty_points_required <= ?
                    AND clm.loyalty_points_required > 0
                    ORDER BY clm.loyalty_points_required ASC
                    LIMIT ?";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$userPoints, $limit]);
            $coupons = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return [
                'success' => true,
                'user_points' => $userPoints,
                'available_coupons' => $coupons,
                'count' => count($coupons)
            ];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * تطبيق كوبون الولاء على عملية شراء
     * Apply loyalty coupon to purchase
     */
    public function applyLoyaltyCouponToPurchase(int $couponId, int $userId, float $purchaseAmount): array {
        try {
            // التحقق من صحة الكوبون
            $coupon = $this->couponManager->getCoupon($couponId);
            if (!$coupon) {
                return ['success' => false, 'error' => 'الكوبون غير موجود'];
            }

            // التحقق من أن الكوبون مرتبط بالولاء
            $loyaltyMapping = $this->getLoyaltyMapping($couponId);
            if (!$loyaltyMapping) {
                return ['success' => false, 'error' => 'هذا الكوبون لا يرتبط بنقاط الولاء'];
            }

            // حساب الخصم
            $discount = 0;
            if ($coupon['type'] === 'fixed') {
                $discount = min($coupon['value'], $purchaseAmount);
            } else if ($coupon['type'] === 'percentage') {
                $discount = ($purchaseAmount * $coupon['value']) / 100;
            }

            // تطبيق الحد الأقصى للخصم إن وجد
            if ($coupon['max_discount'] && $discount > $coupon['max_discount']) {
                $discount = $coupon['max_discount'];
            }

            // منح نقاط الولاء
            $pointsEarned = $this->calculateLoyaltyPointsFromDiscount($discount, $couponId);
            $this->addLoyaltyPoints($userId, $pointsEarned, 'coupon_purchase', "شراء باستخدام كوبون: {$coupon['code']}");

            return [
                'success' => true,
                'original_amount' => $purchaseAmount,
                'discount_amount' => $discount,
                'final_amount' => $purchaseAmount - $discount,
                'loyalty_points_earned' => $pointsEarned,
                'message' => "تم تطبيق الكوبون وكسب {$pointsEarned} نقطة ولاء"
            ];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * الحصول على سجل معاملات الولاء للمستخدم
     * Get loyalty transaction history for user
     */
    public function getUserLoyaltyHistory(int $userId, int $limit = 50, int $offset = 0): array {
        try {
            $sql = "SELECT * FROM loyalty_points_ledger
                    WHERE player_id = ?
                    ORDER BY created_at DESC
                    LIMIT ? OFFSET ?";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$userId, $limit, $offset]);
            $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // الحصول على إجمالي عدد المعاملات
            $countSql = "SELECT COUNT(*) as total FROM loyalty_points_ledger WHERE player_id = ?";
            $countStmt = $this->pdo->prepare($countSql);
            $countStmt->execute([$userId]);
            $total = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];

            return [
                'success' => true,
                'transactions' => $transactions,
                'total' => $total,
                'limit' => $limit,
                'offset' => $offset
            ];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
}
?>

