# دليل تكامل نظام الكوبونات
# Coupon System Integration Guide

## 🔗 التكامل مع نظام الدفع | Payment System Integration

### الخطوة 1: استيراد الفئات
```php
require_once 'includes/CouponManager.php';
require_once 'includes/CouponValidator.php';
require_once 'includes/CouponPaymentIntegration.php';

$couponManager = new CouponManager($pdo);
$couponValidator = new CouponValidator($pdo, $couponManager);
$paymentIntegration = new CouponPaymentIntegration($pdo, $couponManager, $couponValidator);
```

### الخطوة 2: معالجة الدفع مع الكوبون
```php
$paymentData = [
    'coupon_code' => $_POST['coupon_code'] ?? null,
    'amount' => 500,
    'user_id' => $userId,
    'plan_id' => $planId
];

$result = $paymentIntegration->processPaymentWithCoupon($paymentData);

if ($result['success']) {
    // معالجة الدفع بالمبلغ النهائي
    $finalAmount = $result['final_amount'];
    $discountAmount = $result['discount_amount'];
    
    // حفظ معلومات الدفع
    // ...
} else {
    // عرض رسالة الخطأ
    echo "خطأ: " . $result['error'];
}
```

### الخطوة 3: تحديث جداول الدفع
تأكد من أن جدول `payments` يحتوي على الأعمدة التالية:
```sql
ALTER TABLE payments ADD COLUMN coupon_id INT;
ALTER TABLE payments ADD COLUMN discount_amount DECIMAL(10,2) DEFAULT 0;
ALTER TABLE payments ADD COLUMN final_amount DECIMAL(10,2);
ALTER TABLE payments ADD FOREIGN KEY (coupon_id) REFERENCES coupons(id);
```

---

## 🔗 التكامل مع نظام الاشتراكات | Subscription Integration

### الخطوة 1: استيراد الفئات
```php
require_once 'includes/CouponSubscriptionIntegration.php';

$subscriptionIntegration = new CouponSubscriptionIntegration(
    $pdo, 
    $couponManager, 
    $couponValidator
);
```

### الخطوة 2: حساب سعر الاشتراك
```php
// قبل إنشاء الاشتراك
$priceCalc = $subscriptionIntegration->calculateSubscriptionPrice(
    $planId,
    $_POST['coupon_code'] ?? null,
    $userId
);

if ($priceCalc['success']) {
    echo "السعر الأصلي: " . $priceCalc['original_price'];
    echo "الخصم: " . $priceCalc['discount_amount'];
    echo "السعر النهائي: " . $priceCalc['final_price'];
} else {
    echo "خطأ: " . $priceCalc['error'];
}
```

### الخطوة 3: إنشاء اشتراك جديد مع الكوبون
```php
$subscriptionData = [
    'coupon_code' => $_POST['coupon_code'] ?? null,
    'plan_id' => $planId,
    'user_id' => $userId,
    'player_id' => $playerId
];

$result = $subscriptionIntegration->applyToNewSubscription($subscriptionData);

if ($result['success']) {
    $subscriptionId = $result['subscription_id'];
    // معالجة الاشتراك الجديد
} else {
    echo "خطأ: " . $result['error'];
}
```

### الخطوة 4: تطبيق الكوبون على التجديد
```php
$renewalResult = $subscriptionIntegration->applyToRenewal(
    $subscriptionId,
    $_POST['coupon_code'] ?? null
);

if ($renewalResult['success']) {
    echo "تم تجديد الاشتراك بنجاح";
    echo "السعر النهائي: " . $renewalResult['final_amount'];
}
```

### الخطوة 5: تحديث جدول الاشتراكات
```sql
ALTER TABLE subscriptions ADD COLUMN coupon_id INT;
ALTER TABLE subscriptions ADD COLUMN original_amount DECIMAL(10,2);
ALTER TABLE subscriptions ADD COLUMN discount_amount DECIMAL(10,2) DEFAULT 0;
ALTER TABLE subscriptions ADD COLUMN final_amount DECIMAL(10,2);
ALTER TABLE subscriptions ADD FOREIGN KEY (coupon_id) REFERENCES coupons(id);
```

---

## 🔗 التكامل مع نظام الولاء | Loyalty Integration

### الخطوة 1: استيراد الفئات
```php
require_once 'includes/CouponLoyaltyIntegration.php';

$loyaltyIntegration = new CouponLoyaltyIntegration($pdo, $couponManager);
```

### الخطوة 2: منح نقاط الولاء عند استخدام الكوبون
```php
// بعد تطبيق الكوبون بنجاح
$pointsResult = $loyaltyIntegration->awardLoyaltyPoints(
    $couponId,
    $userId,
    $discountAmount
);

if ($pointsResult['success']) {
    echo "تم منح " . $pointsResult['points_awarded'] . " نقطة ولاء";
}
```

### الخطوة 3: استبدال الكوبون بنقاط الولاء
```php
$redeemResult = $loyaltyIntegration->redeemCouponWithLoyaltyPoints(
    $couponId,
    $userId
);

if ($redeemResult['success']) {
    echo "تم استبدال الكوبون: " . $redeemResult['coupon_code'];
    echo "النقاط المتبقية: " . $redeemResult['remaining_points'];
} else {
    echo "خطأ: " . $redeemResult['error'];
}
```

### الخطوة 4: إنشاء كوبون خاص بالولاء
```php
$couponData = [
    'code' => 'LOYALTY100',
    'name' => 'كوبون الولاء',
    'type' => 'fixed',
    'value' => 100,
    'is_active' => 1
];

$loyaltyData = [
    'points_required' => 500,
    'points_earned' => 50
];

$createResult = $loyaltyIntegration->createLoyaltyCoupon(
    $couponData,
    $loyaltyData
);

if ($createResult['success']) {
    echo "تم إنشاء كوبون الولاء بنجاح";
}
```

### الخطوة 5: الحصول على إحصائيات الولاء
```php
$stats = $loyaltyIntegration->getUserLoyaltyStats($userId);

if ($stats['success']) {
    echo "إجمالي النقاط المكتسبة: " . $stats['total_earned'];
    echo "إجمالي النقاط المستخدمة: " . $stats['total_redeemed'];
    echo "الرصيد الحالي: " . $stats['current_balance'];
}
```

---

## 📊 التكامل مع نظام التقارير | Reporting Integration

### الخطوة 1: استيراد الفئات
```php
require_once 'includes/CouponReportingSystem.php';

$reportingSystem = new CouponReportingSystem($pdo);
```

### الخطوة 2: الحصول على تقرير الاستخدام
```php
$usageReport = $reportingSystem->getUsageReport([
    'start_date' => '2025-01-01',
    'end_date' => '2025-01-31',
    'is_active' => 1
]);

foreach ($usageReport['coupons'] as $coupon) {
    echo "الكوبون: " . $coupon['code'];
    echo "عدد الاستخدامات: " . $coupon['usage_count'];
    echo "إجمالي الخصم: " . $coupon['total_discount'];
}
```

### الخطوة 3: الحصول على تقرير الإيرادات
```php
$revenueReport = $reportingSystem->getRevenueReport([
    'start_date' => '2025-01-01',
    'end_date' => '2025-01-31'
]);

foreach ($revenueReport['daily_report'] as $day) {
    echo "التاريخ: " . $day['date'];
    echo "الإيرادات الإجمالية: " . $day['gross_revenue'];
    echo "إجمالي الخصم: " . $day['total_discount'];
    echo "الإيرادات الصافية: " . $day['net_revenue'];
}
```

### الخطوة 4: الحصول على أفضل الكوبونات
```php
$topCoupons = $reportingSystem->getTopCouponsReport(10);

foreach ($topCoupons['top_coupons'] as $coupon) {
    echo "الكوبون: " . $coupon['code'];
    echo "عدد الاستخدامات: " . $coupon['usage_count'];
    echo "عدد المستخدمين الفريدين: " . $coupon['unique_users'];
}
```

### الخطوة 5: الحصول على الكوبونات القريبة من الانتهاء
```php
$expiringCoupons = $reportingSystem->getExpiringCouponsReport(7);

foreach ($expiringCoupons['expiring_coupons'] as $coupon) {
    echo "الكوبون: " . $coupon['code'];
    echo "الأيام المتبقية: " . $coupon['days_remaining'];
}
```

---

## 🔄 سير العمل الكامل | Complete Workflow

### سيناريو: عملية شراء اشتراك مع كوبون

```php
// 1. التحقق من الكوبون
$validation = $couponValidator->validate('SUMMER20', [
    'amount' => 500,
    'user_id' => $userId,
    'plan_id' => $planId
]);

if (!$validation['valid']) {
    die("الكوبون غير صحيح: " . $validation['error']);
}

// 2. حساب السعر
$priceCalc = $subscriptionIntegration->calculateSubscriptionPrice(
    $planId,
    'SUMMER20',
    $userId
);

// 3. إنشاء الاشتراك
$subscription = $subscriptionIntegration->applyToNewSubscription([
    'coupon_code' => 'SUMMER20',
    'plan_id' => $planId,
    'user_id' => $userId,
    'player_id' => $playerId
]);

// 4. معالجة الدفع
$payment = $paymentIntegration->processPaymentWithCoupon([
    'coupon_code' => 'SUMMER20',
    'amount' => $priceCalc['original_price'],
    'user_id' => $userId,
    'plan_id' => $planId
]);

// 5. منح نقاط الولاء
$loyaltyIntegration->awardLoyaltyPoints(
    $validation['coupon']['id'],
    $userId,
    $payment['discount_amount']
);

// 6. تسجيل الدفعة
// INSERT INTO payments (subscription_id, coupon_id, amount, discount_amount, final_amount, ...)

// 7. إرسال تأكيد للمستخدم
// Email confirmation with subscription details and discount applied
```

---

## ⚠️ نقاط مهمة | Important Notes

1. **تحديث الجداول:** تأكد من تحديث جداول الدفع والاشتراكات بالأعمدة المطلوبة
2. **الأمان:** استخدم دائماً Prepared Statements
3. **التحقق:** تحقق دائماً من نتائج العمليات قبل المتابعة
4. **السجلات:** تتم جميع العمليات تسجيلها تلقائياً في جدول التدقيق
5. **الأداء:** استخدم الفهارس على الأعمدة المستخدمة بكثرة

---

## 🐛 استكشاف الأخطاء | Troubleshooting

### المشكلة: "الكوبون غير موجود"
**الحل:** تأكد من أن الكوبون موجود وفعال في قاعدة البيانات

### المشكلة: "تم تجاوز حد الاستخدام"
**الحل:** تحقق من `usage_limit` و `used_count` في جدول الكوبونات

### المشكلة: "انتهت صلاحية الكوبون"
**الحل:** تحقق من `valid_from` و `valid_until` في جدول الكوبونات

### المشكلة: "المبلغ أقل من الحد الأدنى"
**الحل:** تأكد من أن المبلغ أكبر من أو يساوي `minimum_amount`

---

## 📞 الدعم والمساعدة | Support

للمزيد من المعلومات أو الدعم، يرجى التواصل مع فريق التطوير.

