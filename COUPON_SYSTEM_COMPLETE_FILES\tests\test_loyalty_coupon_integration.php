<?php
/**
 * اختبارات تكامل الكوبونات مع الولاء
 * Loyalty Coupon Integration Tests
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/CouponManager.php';
require_once __DIR__ . '/../includes/CouponLoyaltyIntegration.php';

class LoyaltyCouponIntegrationTest {
    private $pdo;
    private $couponManager;
    private $loyaltyIntegration;
    private $testResults = [];
    
    public function __construct(PDO $pdo) {
        $this->pdo = $pdo;
        $this->couponManager = new CouponManager($pdo);
        $this->loyaltyIntegration = new CouponLoyaltyIntegration($pdo, $this->couponManager);
    }
    
    public function runAllTests() {
        echo "🧪 بدء اختبارات تكامل الكوبونات مع الولاء\n";
        echo "Starting Loyalty Coupon Integration Tests\n\n";
        
        $this->testCreateLoyaltyCoupon();
        $this->testRedeemCouponWithPoints();
        $this->testAwardLoyaltyPoints();
        $this->testCalculateLoyaltyPoints();
        $this->testGetAvailableLoyaltyCoupons();
        $this->testApplyLoyaltyCouponToPurchase();
        $this->testGetUserLoyaltyStats();
        $this->testGetUserLoyaltyHistory();
        
        $this->printResults();
    }
    
    private function testCreateLoyaltyCoupon() {
        echo "1️⃣ اختبار إنشاء كوبون ولاء جديد...\n";
        
        try {
            $couponData = [
                'code' => 'LOYALTY_TEST_' . time(),
                'name' => 'كوبون اختبار الولاء',
                'type' => 'fixed',
                'value' => 50,
                'is_active' => 1
            ];
            
            $loyaltyData = [
                'points_required' => 100,
                'points_earned' => 10
            ];
            
            $result = $this->loyaltyIntegration->createLoyaltyCoupon($couponData, $loyaltyData);
            
            if ($result['success']) {
                echo "✅ تم إنشاء كوبون الولاء بنجاح (ID: {$result['coupon_id']})\n";
                $this->testResults[] = ['test' => 'Create Loyalty Coupon', 'status' => 'PASS'];
            } else {
                echo "❌ فشل الاختبار: " . $result['error'] . "\n";
                $this->testResults[] = ['test' => 'Create Loyalty Coupon', 'status' => 'FAIL'];
            }
        } catch (Exception $e) {
            echo "❌ خطأ: " . $e->getMessage() . "\n";
            $this->testResults[] = ['test' => 'Create Loyalty Coupon', 'status' => 'ERROR'];
        }
        echo "\n";
    }
    
    private function testRedeemCouponWithPoints() {
        echo "2️⃣ اختبار استبدال كوبون بنقاط الولاء...\n";
        
        try {
            // إنشاء كوبون أولاً
            $couponData = [
                'code' => 'REDEEM_TEST_' . time(),
                'name' => 'كوبون الاستبدال',
                'type' => 'fixed',
                'value' => 100,
                'is_active' => 1
            ];
            
            $loyaltyData = [
                'points_required' => 50,
                'points_earned' => 5
            ];
            
            $createResult = $this->loyaltyIntegration->createLoyaltyCoupon($couponData, $loyaltyData);
            
            if ($createResult['success']) {
                // محاولة الاستبدال
                $result = $this->loyaltyIntegration->redeemCouponWithLoyaltyPoints(
                    $createResult['coupon_id'],
                    1 // معرف المستخدم
                );
                
                if ($result['success'] || strpos($result['error'] ?? '', 'أقل من المطلوب') !== false) {
                    echo "✅ اختبار الاستبدال نجح\n";
                    $this->testResults[] = ['test' => 'Redeem Coupon with Points', 'status' => 'PASS'];
                } else {
                    echo "❌ فشل الاختبار: " . $result['error'] . "\n";
                    $this->testResults[] = ['test' => 'Redeem Coupon with Points', 'status' => 'FAIL'];
                }
            }
        } catch (Exception $e) {
            echo "❌ خطأ: " . $e->getMessage() . "\n";
            $this->testResults[] = ['test' => 'Redeem Coupon with Points', 'status' => 'ERROR'];
        }
        echo "\n";
    }
    
    private function testAwardLoyaltyPoints() {
        echo "3️⃣ اختبار منح نقاط الولاء...\n";
        
        try {
            $result = $this->loyaltyIntegration->awardLoyaltyPoints(1, 1, 100);
            
            if ($result['success']) {
                echo "✅ تم منح النقاط بنجاح (النقاط: {$result['points_awarded']})\n";
                $this->testResults[] = ['test' => 'Award Loyalty Points', 'status' => 'PASS'];
            } else {
                echo "❌ فشل الاختبار: " . $result['error'] . "\n";
                $this->testResults[] = ['test' => 'Award Loyalty Points', 'status' => 'FAIL'];
            }
        } catch (Exception $e) {
            echo "❌ خطأ: " . $e->getMessage() . "\n";
            $this->testResults[] = ['test' => 'Award Loyalty Points', 'status' => 'ERROR'];
        }
        echo "\n";
    }
    
    private function testCalculateLoyaltyPoints() {
        echo "4️⃣ اختبار حساب نقاط الولاء من الخصم...\n";
        
        try {
            $points = $this->loyaltyIntegration->calculateLoyaltyPointsFromDiscount(100);
            
            if ($points > 0) {
                echo "✅ تم حساب النقاط بنجاح (النقاط: {$points})\n";
                $this->testResults[] = ['test' => 'Calculate Loyalty Points', 'status' => 'PASS'];
            } else {
                echo "❌ فشل الاختبار: النقاط = {$points}\n";
                $this->testResults[] = ['test' => 'Calculate Loyalty Points', 'status' => 'FAIL'];
            }
        } catch (Exception $e) {
            echo "❌ خطأ: " . $e->getMessage() . "\n";
            $this->testResults[] = ['test' => 'Calculate Loyalty Points', 'status' => 'ERROR'];
        }
        echo "\n";
    }
    
    private function testGetAvailableLoyaltyCoupons() {
        echo "5️⃣ اختبار الحصول على الكوبونات المتاحة...\n";
        
        try {
            $result = $this->loyaltyIntegration->getAvailableLoyaltyCoupons(1, 5);
            
            if ($result['success']) {
                echo "✅ تم جلب الكوبونات بنجاح (العدد: {$result['count']})\n";
                $this->testResults[] = ['test' => 'Get Available Loyalty Coupons', 'status' => 'PASS'];
            } else {
                echo "❌ فشل الاختبار: " . $result['error'] . "\n";
                $this->testResults[] = ['test' => 'Get Available Loyalty Coupons', 'status' => 'FAIL'];
            }
        } catch (Exception $e) {
            echo "❌ خطأ: " . $e->getMessage() . "\n";
            $this->testResults[] = ['test' => 'Get Available Loyalty Coupons', 'status' => 'ERROR'];
        }
        echo "\n";
    }
    
    private function testApplyLoyaltyCouponToPurchase() {
        echo "6️⃣ اختبار تطبيق كوبون الولاء على عملية شراء...\n";
        
        try {
            // إنشاء كوبون أولاً
            $couponData = [
                'code' => 'PURCHASE_TEST_' . time(),
                'name' => 'كوبون الشراء',
                'type' => 'percentage',
                'value' => 10,
                'is_active' => 1
            ];
            
            $loyaltyData = [
                'points_required' => 0,
                'points_earned' => 5
            ];
            
            $createResult = $this->loyaltyIntegration->createLoyaltyCoupon($couponData, $loyaltyData);
            
            if ($createResult['success']) {
                $result = $this->loyaltyIntegration->applyLoyaltyCouponToPurchase(
                    $createResult['coupon_id'],
                    1,
                    1000
                );
                
                if ($result['success']) {
                    echo "✅ تم تطبيق الكوبون بنجاح (الخصم: {$result['discount_amount']})\n";
                    $this->testResults[] = ['test' => 'Apply Loyalty Coupon to Purchase', 'status' => 'PASS'];
                } else {
                    echo "❌ فشل الاختبار: " . $result['error'] . "\n";
                    $this->testResults[] = ['test' => 'Apply Loyalty Coupon to Purchase', 'status' => 'FAIL'];
                }
            }
        } catch (Exception $e) {
            echo "❌ خطأ: " . $e->getMessage() . "\n";
            $this->testResults[] = ['test' => 'Apply Loyalty Coupon to Purchase', 'status' => 'ERROR'];
        }
        echo "\n";
    }
    
    private function testGetUserLoyaltyStats() {
        echo "7️⃣ اختبار الحصول على إحصائيات الولاء...\n";
        
        try {
            $result = $this->loyaltyIntegration->getUserLoyaltyStats(1);
            
            if ($result['success']) {
                echo "✅ تم جلب الإحصائيات بنجاح\n";
                echo "   - النقاط المكتسبة: {$result['total_earned']}\n";
                echo "   - النقاط المستخدمة: {$result['total_redeemed']}\n";
                echo "   - الرصيد الحالي: {$result['current_balance']}\n";
                $this->testResults[] = ['test' => 'Get User Loyalty Stats', 'status' => 'PASS'];
            } else {
                echo "❌ فشل الاختبار: " . $result['error'] . "\n";
                $this->testResults[] = ['test' => 'Get User Loyalty Stats', 'status' => 'FAIL'];
            }
        } catch (Exception $e) {
            echo "❌ خطأ: " . $e->getMessage() . "\n";
            $this->testResults[] = ['test' => 'Get User Loyalty Stats', 'status' => 'ERROR'];
        }
        echo "\n";
    }
    
    private function testGetUserLoyaltyHistory() {
        echo "8️⃣ اختبار الحصول على سجل المعاملات...\n";
        
        try {
            $result = $this->loyaltyIntegration->getUserLoyaltyHistory(1, 10, 0);
            
            if ($result['success']) {
                echo "✅ تم جلب السجل بنجاح (العدد: {$result['total']})\n";
                $this->testResults[] = ['test' => 'Get User Loyalty History', 'status' => 'PASS'];
            } else {
                echo "❌ فشل الاختبار: " . $result['error'] . "\n";
                $this->testResults[] = ['test' => 'Get User Loyalty History', 'status' => 'FAIL'];
            }
        } catch (Exception $e) {
            echo "❌ خطأ: " . $e->getMessage() . "\n";
            $this->testResults[] = ['test' => 'Get User Loyalty History', 'status' => 'ERROR'];
        }
        echo "\n";
    }
    
    private function printResults() {
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "📊 ملخص النتائج | Test Results Summary\n";
        echo str_repeat("=", 60) . "\n\n";
        
        $passed = 0;
        $failed = 0;
        $errors = 0;
        
        foreach ($this->testResults as $result) {
            $status = $result['status'];
            if ($status === 'PASS') {
                echo "✅ {$result['test']}: PASS\n";
                $passed++;
            } elseif ($status === 'FAIL') {
                echo "❌ {$result['test']}: FAIL\n";
                $failed++;
            } else {
                echo "⚠️ {$result['test']}: ERROR\n";
                $errors++;
            }
        }
        
        echo "\n" . str_repeat("-", 60) . "\n";
        echo "الإجمالي: " . count($this->testResults) . " | ";
        echo "نجح: $passed | ";
        echo "فشل: $failed | ";
        echo "أخطاء: $errors\n";
        echo str_repeat("-", 60) . "\n";
    }
}

// تشغيل الاختبارات
$test = new LoyaltyCouponIntegrationTest($pdo);
$test->runAllTests();
?>

