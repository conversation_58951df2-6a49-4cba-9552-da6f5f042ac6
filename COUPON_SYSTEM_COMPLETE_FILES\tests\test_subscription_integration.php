<?php
/**
 * اختبارات تكامل الكوبونات مع الاشتراكات
 * Subscription Integration Tests
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/CouponManager.php';
require_once __DIR__ . '/../includes/CouponValidator.php';
require_once __DIR__ . '/../includes/CouponSubscriptionIntegration.php';

class SubscriptionIntegrationTest {
    private $pdo;
    private $couponManager;
    private $couponValidator;
    private $subscriptionIntegration;
    private $testResults = [];
    
    public function __construct() {
        $this->pdo = getDBConnection();
        $this->couponManager = new CouponManager($this->pdo);
        $this->couponValidator = new CouponValidator($this->pdo, $this->couponManager);
        $this->subscriptionIntegration = new CouponSubscriptionIntegration(
            $this->pdo,
            $this->couponManager,
            $this->couponValidator
        );
    }
    
    public function runAllTests() {
        echo "🧪 بدء اختبارات تكامل الكوبونات مع الاشتراكات\n";
        echo str_repeat("=", 70) . "\n\n";
        
        $this->testCalculateSubscriptionPrice();
        $this->testApplyToNewSubscription();
        $this->testApplyToRenewal();
        $this->testApplyToUpgrade();
        $this->testRemoveCoupon();
        $this->testValidateForSubscription();
        
        $this->printResults();
    }
    
    private function testCalculateSubscriptionPrice() {
        echo "📝 اختبار 1: حساب سعر الاشتراك مع الكوبون\n";
        
        try {
            // الحصول على خطة موجودة
            $stmt = $this->pdo->query("SELECT id FROM subscription_plans LIMIT 1");
            $plan = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$plan) {
                $this->testResults[] = ['name' => 'حساب السعر', 'status' => '⚠️ تحذير', 'message' => 'لا توجد خطط'];
                return;
            }
            
            $result = $this->subscriptionIntegration->calculateSubscriptionPrice($plan['id']);
            
            if ($result['success']) {
                echo "  ✅ تم حساب السعر بنجاح\n";
                echo "  السعر الأصلي: " . $result['original_price'] . "\n";
                echo "  السعر النهائي: " . $result['final_price'] . "\n";
                $this->testResults[] = ['name' => 'حساب السعر', 'status' => '✅ نجح', 'message' => 'تم حساب السعر بنجاح'];
            } else {
                echo "  ❌ فشل: " . $result['error'] . "\n";
                $this->testResults[] = ['name' => 'حساب السعر', 'status' => '❌ فشل', 'message' => $result['error']];
            }
        } catch (Exception $e) {
            echo "  ❌ استثناء: " . $e->getMessage() . "\n";
            $this->testResults[] = ['name' => 'حساب السعر', 'status' => '❌ خطأ', 'message' => $e->getMessage()];
        }
        echo "\n";
    }
    
    private function testApplyToNewSubscription() {
        echo "📝 اختبار 2: تطبيق الكوبون على اشتراك جديد\n";
        
        try {
            $stmt = $this->pdo->query("SELECT id FROM subscription_plans LIMIT 1");
            $plan = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$plan) {
                $this->testResults[] = ['name' => 'اشتراك جديد', 'status' => '⚠️ تحذير', 'message' => 'لا توجد خطط'];
                return;
            }
            
            $subscriptionData = [
                'plan_id' => $plan['id'],
                'player_id' => 1,
                'user_id' => 1,
                'coupon_code' => null
            ];
            
            $result = $this->subscriptionIntegration->applyToNewSubscription($subscriptionData);
            
            if ($result['success']) {
                echo "  ✅ تم إنشاء الاشتراك بنجاح\n";
                echo "  معرف الاشتراك: " . $result['subscription_id'] . "\n";
                $this->testResults[] = ['name' => 'اشتراك جديد', 'status' => '✅ نجح', 'message' => 'تم الإنشاء بنجاح'];
            } else {
                echo "  ❌ فشل: " . $result['error'] . "\n";
                $this->testResults[] = ['name' => 'اشتراك جديد', 'status' => '❌ فشل', 'message' => $result['error']];
            }
        } catch (Exception $e) {
            echo "  ❌ استثناء: " . $e->getMessage() . "\n";
            $this->testResults[] = ['name' => 'اشتراك جديد', 'status' => '❌ خطأ', 'message' => $e->getMessage()];
        }
        echo "\n";
    }
    
    private function testApplyToRenewal() {
        echo "📝 اختبار 3: تطبيق الكوبون على تجديد الاشتراك\n";
        
        try {
            $stmt = $this->pdo->query("SELECT id FROM player_subscriptions LIMIT 1");
            $subscription = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$subscription) {
                $this->testResults[] = ['name' => 'التجديد', 'status' => '⚠️ تحذير', 'message' => 'لا توجد اشتراكات'];
                return;
            }
            
            $result = $this->subscriptionIntegration->applyToRenewal($subscription['id']);
            
            if ($result['success']) {
                echo "  ✅ تم تجديد الاشتراك بنجاح\n";
                $this->testResults[] = ['name' => 'التجديد', 'status' => '✅ نجح', 'message' => 'تم التجديد بنجاح'];
            } else {
                echo "  ❌ فشل: " . $result['error'] . "\n";
                $this->testResults[] = ['name' => 'التجديد', 'status' => '❌ فشل', 'message' => $result['error']];
            }
        } catch (Exception $e) {
            echo "  ❌ استثناء: " . $e->getMessage() . "\n";
            $this->testResults[] = ['name' => 'التجديد', 'status' => '❌ خطأ', 'message' => $e->getMessage()];
        }
        echo "\n";
    }
    
    private function testApplyToUpgrade() {
        echo "📝 اختبار 4: تطبيق الكوبون على ترقية الاشتراك\n";
        
        try {
            $stmt = $this->pdo->query("SELECT id FROM player_subscriptions LIMIT 1");
            $subscription = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $stmt = $this->pdo->query("SELECT id FROM subscription_plans LIMIT 1");
            $plan = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$subscription || !$plan) {
                $this->testResults[] = ['name' => 'الترقية', 'status' => '⚠️ تحذير', 'message' => 'بيانات ناقصة'];
                return;
            }
            
            $result = $this->subscriptionIntegration->applyToUpgrade($subscription['id'], $plan['id']);
            
            if ($result['success']) {
                echo "  ✅ تم ترقية الاشتراك بنجاح\n";
                $this->testResults[] = ['name' => 'الترقية', 'status' => '✅ نجح', 'message' => 'تم الترقية بنجاح'];
            } else {
                echo "  ❌ فشل: " . $result['error'] . "\n";
                $this->testResults[] = ['name' => 'الترقية', 'status' => '❌ فشل', 'message' => $result['error']];
            }
        } catch (Exception $e) {
            echo "  ❌ استثناء: " . $e->getMessage() . "\n";
            $this->testResults[] = ['name' => 'الترقية', 'status' => '❌ خطأ', 'message' => $e->getMessage()];
        }
        echo "\n";
    }
    
    private function testRemoveCoupon() {
        echo "📝 اختبار 5: إزالة الكوبون من الاشتراك\n";
        
        try {
            $stmt = $this->pdo->query("SELECT id FROM player_subscriptions WHERE coupon_id IS NOT NULL LIMIT 1");
            $subscription = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$subscription) {
                $this->testResults[] = ['name' => 'إزالة الكوبون', 'status' => '⚠️ تحذير', 'message' => 'لا توجد اشتراكات بكوبونات'];
                return;
            }
            
            $result = $this->subscriptionIntegration->removeCoupon($subscription['id']);
            
            if ($result['success']) {
                echo "  ✅ تم إزالة الكوبون بنجاح\n";
                $this->testResults[] = ['name' => 'إزالة الكوبون', 'status' => '✅ نجح', 'message' => 'تم الإزالة بنجاح'];
            } else {
                echo "  ❌ فشل: " . $result['error'] . "\n";
                $this->testResults[] = ['name' => 'إزالة الكوبون', 'status' => '❌ فشل', 'message' => $result['error']];
            }
        } catch (Exception $e) {
            echo "  ❌ استثناء: " . $e->getMessage() . "\n";
            $this->testResults[] = ['name' => 'إزالة الكوبون', 'status' => '❌ خطأ', 'message' => $e->getMessage()];
        }
        echo "\n";
    }
    
    private function testValidateForSubscription() {
        echo "📝 اختبار 6: التحقق من صحة الكوبون للاشتراك\n";
        
        try {
            $stmt = $this->pdo->query("SELECT code FROM coupons WHERE status = 'active' LIMIT 1");
            $coupon = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$coupon) {
                $this->testResults[] = ['name' => 'التحقق', 'status' => '⚠️ تحذير', 'message' => 'لا توجد كوبونات'];
                return;
            }
            
            $context = ['amount' => 100, 'plan_id' => 1, 'user_id' => 1];
            $validation = $this->couponValidator->validate($coupon['code'], $context);
            
            if ($validation['valid']) {
                echo "  ✅ الكوبون صحيح\n";
                $this->testResults[] = ['name' => 'التحقق', 'status' => '✅ نجح', 'message' => 'الكوبون صحيح'];
            } else {
                echo "  ⚠️  الكوبون غير صحيح: " . $validation['error'] . "\n";
                $this->testResults[] = ['name' => 'التحقق', 'status' => '⚠️ تحذير', 'message' => $validation['error']];
            }
        } catch (Exception $e) {
            echo "  ❌ استثناء: " . $e->getMessage() . "\n";
            $this->testResults[] = ['name' => 'التحقق', 'status' => '❌ خطأ', 'message' => $e->getMessage()];
        }
        echo "\n";
    }
    
    private function printResults() {
        echo str_repeat("=", 70) . "\n";
        echo "📊 ملخص النتائج\n";
        echo str_repeat("=", 70) . "\n\n";
        
        echo sprintf("%-30s | %-15s | %s\n", "الاختبار", "الحالة", "الرسالة");
        echo str_repeat("-", 70) . "\n";
        
        foreach ($this->testResults as $result) {
            echo sprintf("%-30s | %-15s | %s\n", 
                substr($result['name'], 0, 28),
                $result['status'],
                substr($result['message'], 0, 30)
            );
        }
        
        echo "\n" . str_repeat("=", 70) . "\n";
        echo "✅ انتهت الاختبارات\n";
    }
}

// تشغيل الاختبارات
$test = new SubscriptionIntegrationTest();
$test->runAllTests();
?>

