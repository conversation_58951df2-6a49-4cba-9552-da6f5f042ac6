<?php
/**
 * مدير الكوبونات - Coupon Manager
 * إدارة شاملة لنظام الكوبونات مع التكامل الكامل
 */

class CouponManager {
    private $pdo;
    private $logger;
    
    public function __construct(PDO $pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * إنشاء كوبون جديد
     */
    public function createCoupon(array $data): array {
        try {
            $uuid = $this->generateUUID();
            
            $sql = "INSERT INTO coupons (
                uuid, code, name, description, type, value,
                minimum_amount, maximum_discount, usage_limit,
                usage_limit_per_user, valid_from, valid_until,
                is_active, applicable_to, user_restrictions,
                created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([
                $uuid,
                $data['code'],
                $data['name'],
                $data['description'] ?? null,
                $data['type'] ?? 'percentage',
                $data['value'],
                $data['minimum_amount'] ?? 0,
                $data['maximum_discount'] ?? null,
                $data['usage_limit'] ?? null,
                $data['usage_limit_per_user'] ?? 1,
                $data['valid_from'],
                $data['valid_until'],
                $data['is_active'] ?? true,
                isset($data['applicable_to']) ? json_encode($data['applicable_to']) : null,
                isset($data['user_restrictions']) ? json_encode($data['user_restrictions']) : null,
                $data['created_by'] ?? null
            ]);
            
            $couponId = $this->pdo->lastInsertId();
            
            // تسجيل في سجل التدقيق
            $this->logAudit($couponId, 'create', null, $data, $data['created_by'] ?? null);
            
            return ['success' => true, 'id' => $couponId, 'uuid' => $uuid];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * تحديث كوبون
     */
    public function updateCoupon(int $couponId, array $data): array {
        try {
            $oldData = $this->getCoupon($couponId);
            
            $updates = [];
            $values = [];
            
            foreach ($data as $key => $value) {
                if (in_array($key, ['code', 'name', 'description', 'type', 'value', 
                    'minimum_amount', 'maximum_discount', 'usage_limit', 
                    'usage_limit_per_user', 'valid_from', 'valid_until', 'is_active'])) {
                    $updates[] = "`$key` = ?";
                    $values[] = $value;
                }
            }
            
            if (empty($updates)) return ['success' => false, 'error' => 'لا توجد حقول للتحديث'];
            
            $values[] = $couponId;
            $sql = "UPDATE coupons SET " . implode(', ', $updates) . " WHERE id = ?";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($values);
            
            // تسجيل في سجل التدقيق
            $this->logAudit($couponId, 'update', $oldData, $data, $data['updated_by'] ?? null);
            
            return ['success' => true];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * الحصول على كوبون
     */
    public function getCoupon(int $couponId): ?array {
        $sql = "SELECT * FROM coupons WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$couponId]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }
    
    /**
     * الحصول على كوبون بالكود
     */
    public function getCouponByCode(string $code): ?array {
        $sql = "SELECT * FROM coupons WHERE code = ? AND is_active = 1";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$code]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }
    
    /**
     * التحقق من صحة الكوبون
     */
    public function validateCoupon(string $code, float $amount, ?int $userId = null, ?int $planId = null): array {
        $coupon = $this->getCouponByCode($code);
        
        if (!$coupon) {
            return ['valid' => false, 'error' => 'الكوبون غير موجود'];
        }
        
        if (!$coupon['is_active']) {
            return ['valid' => false, 'error' => 'الكوبون معطل'];
        }
        
        $now = new DateTime();
        $validFrom = new DateTime($coupon['valid_from']);
        $validUntil = new DateTime($coupon['valid_until']);
        
        if ($now < $validFrom || $now > $validUntil) {
            return ['valid' => false, 'error' => 'الكوبون منتهي الصلاحية'];
        }
        
        if ($coupon['usage_limit'] && $coupon['used_count'] >= $coupon['usage_limit']) {
            return ['valid' => false, 'error' => 'تم استنفاد حد الاستخدام'];
        }
        
        if ($amount < $coupon['minimum_amount']) {
            return ['valid' => false, 'error' => 'المبلغ أقل من الحد الأدنى'];
        }
        
        if ($userId) {
            $userUsageCount = $this->getUserCouponUsageCount($coupon['id'], $userId);
            if ($userUsageCount >= $coupon['usage_limit_per_user']) {
                return ['valid' => false, 'error' => 'لقد استخدمت هذا الكوبون بالفعل'];
            }
        }
        
        return ['valid' => true, 'coupon' => $coupon];
    }
    
    /**
     * حساب الخصم
     */
    public function calculateDiscount(array $coupon, float $amount): float {
        $discount = 0;
        
        if ($coupon['type'] === 'percentage') {
            $discount = ($amount * $coupon['value']) / 100;
        } elseif ($coupon['type'] === 'fixed') {
            $discount = $coupon['value'];
        }
        
        if ($coupon['maximum_discount'] && $discount > $coupon['maximum_discount']) {
            $discount = $coupon['maximum_discount'];
        }
        
        return min($discount, $amount);
    }
    
    /**
     * تطبيق الكوبون
     */
    public function applyCoupon(int $couponId, int $userId, float $originalAmount, ?int $subscriptionId = null, ?int $paymentId = null): array {
        try {
            $coupon = $this->getCoupon($couponId);
            if (!$coupon) return ['success' => false, 'error' => 'الكوبون غير موجود'];
            
            $discount = $this->calculateDiscount($coupon, $originalAmount);
            $finalAmount = $originalAmount - $discount;
            
            $sql = "INSERT INTO coupon_usage (
                coupon_id, user_id, subscription_id, payment_id,
                discount_amount, original_amount, final_amount
            ) VALUES (?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([
                $couponId, $userId, $subscriptionId, $paymentId,
                $discount, $originalAmount, $finalAmount
            ]);
            
            // تحديث عدد الاستخدام
            $this->pdo->prepare("UPDATE coupons SET used_count = used_count + 1 WHERE id = ?")
                ->execute([$couponId]);
            
            return [
                'success' => true,
                'discount' => $discount,
                'final_amount' => $finalAmount
            ];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * الحصول على عدد استخدام الكوبون للمستخدم
     */
    private function getUserCouponUsageCount(int $couponId, int $userId): int {
        $sql = "SELECT COUNT(*) as count FROM coupon_usage 
                WHERE coupon_id = ? AND user_id = ? AND status = 'applied'";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$couponId, $userId]);
        return (int)$stmt->fetch(PDO::FETCH_ASSOC)['count'];
    }
    
    /**
     * تسجيل في سجل التدقيق
     */
    private function logAudit(int $couponId, string $action, ?array $oldValues, ?array $newValues, ?int $performedBy): void {
        $sql = "INSERT INTO coupon_audit_log (coupon_id, action, old_values, new_values, performed_by)
                VALUES (?, ?, ?, ?, ?)";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([
            $couponId,
            $action,
            $oldValues ? json_encode($oldValues) : null,
            $newValues ? json_encode($newValues) : null,
            $performedBy
        ]);
    }
    
    /**
     * توليد UUID
     */
    private function generateUUID(): string {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
}
?>

