<?php
/**
 * اختبار نهائي لنظام الكوبونات
 * Final Coupon System Test
 */

echo "🧪 اختبار نهائي لنظام الكوبونات\n";
echo "===============================\n\n";

// إعداد قاعدة البيانات
try {
    // تضمين ملف إعدادات قاعدة البيانات
    require_once __DIR__ . '/../config/database.php';
    
    echo "✅ تم تحميل إعدادات قاعدة البيانات\n";
    echo "📊 المضيف: " . DB_HOST . "\n";
    echo "📊 قاعدة البيانات: " . DB_NAME . "\n";
    echo "📊 المستخدم: " . DB_USER . "\n\n";

    // استخدام الاتصال الموجود
    if (!isset($pdo)) {
        $pdo = getDBConnection();
    }
    
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح\n\n";
    
    // 1. إنشاء الجداول من ملف SQL
    echo "📋 إنشاء جداول الكوبونات...\n";
    try {
        $sqlFile = __DIR__ . '/../database/final_coupon_setup.sql';
        if (file_exists($sqlFile)) {
            $sql = file_get_contents($sqlFile);
            
            // تقسيم الاستعلامات
            $statements = array_filter(array_map('trim', explode(';', $sql)));
            
            foreach ($statements as $statement) {
                if (!empty($statement) && !preg_match('/^--/', $statement)) {
                    try {
                        $pdo->exec($statement);
                    } catch (Exception $e) {
                        // تجاهل أخطاء SELECT (رسائل النجاح)
                        if (!preg_match('/SELECT.*message/', $statement)) {
                            echo "⚠️ تحذير في الاستعلام: " . substr($statement, 0, 50) . "...\n";
                            echo "   الخطأ: " . $e->getMessage() . "\n";
                        }
                    }
                }
            }
            echo "✅ تم تنفيذ ملف SQL بنجاح\n";
        } else {
            echo "❌ ملف SQL غير موجود: $sqlFile\n";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في إنشاء الجداول: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 2. فحص الجداول المُنشأة
    echo "📋 فحص الجداول المُنشأة...\n";
    $tables = ['coupons', 'coupon_usage'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                echo "✅ جدول $table: موجود\n";
                
                // عدد السجلات
                $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
                $count = $stmt->fetchColumn();
                echo "   📊 عدد السجلات: $count\n";
                
                // فحص الأعمدة المهمة
                if ($table === 'coupons') {
                    $stmt = $pdo->query("DESCRIBE `$table`");
                    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                    $requiredColumns = ['id', 'code', 'name', 'type', 'value', 'is_active'];
                    foreach ($requiredColumns as $col) {
                        if (in_array($col, $columns)) {
                            echo "   ✅ عمود $col: موجود\n";
                        } else {
                            echo "   ❌ عمود $col: مفقود\n";
                        }
                    }
                }
            } else {
                echo "❌ جدول $table: غير موجود\n";
            }
        } catch (Exception $e) {
            echo "❌ خطأ في فحص جدول $table: " . $e->getMessage() . "\n";
        }
        echo "\n";
    }
    
    // 3. اختبار الكوبونات
    echo "📋 اختبار الكوبونات الموجودة...\n";
    try {
        $stmt = $pdo->query("SELECT id, code, name, type, value, is_active FROM coupons ORDER BY id");
        $coupons = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($coupons) > 0) {
            echo "✅ تم العثور على " . count($coupons) . " كوبون\n";
            foreach ($coupons as $coupon) {
                $status = $coupon['is_active'] ? '🟢 مفعل' : '🔴 معطل';
                echo "  - {$coupon['id']}: {$coupon['code']} - {$coupon['name']}\n";
                echo "    النوع: {$coupon['type']}, القيمة: {$coupon['value']}, الحالة: $status\n";
            }
        } else {
            echo "⚠️ لا توجد كوبونات\n";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في اختبار الكوبونات: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 4. اختبار إنشاء كوبون جديد
    echo "📋 اختبار إنشاء كوبون جديد...\n";
    try {
        $sql = "INSERT INTO coupons (uuid, code, name, description, type, value, minimum_amount, valid_from, valid_until, is_active) 
                VALUES (UUID(), ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            'NEWTEST2025',
            'كوبون جديد للاختبار',
            'كوبون تم إنشاؤه من الاختبار',
            'percentage',
            20.00,
            25.00,
            '2025-01-01 00:00:00',
            '2025-12-31 23:59:59',
            1
        ]);
        
        if ($result) {
            $couponId = $pdo->lastInsertId();
            echo "✅ تم إنشاء كوبون جديد بنجاح - المعرف: $couponId\n";
        } else {
            echo "❌ فشل في إنشاء الكوبون\n";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في إنشاء الكوبون: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 5. ملخص النتائج النهائي
    echo "📊 ملخص النتائج النهائي:\n";
    echo "========================\n";
    
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM coupons");
        $totalCoupons = $stmt->fetchColumn();
        echo "✅ إجمالي الكوبونات: $totalCoupons\n";
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM coupons WHERE is_active = 1");
        $activeCoupons = $stmt->fetchColumn();
        echo "✅ الكوبونات المفعلة: $activeCoupons\n";
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM coupon_usage");
        $usageCount = $stmt->fetchColumn();
        echo "✅ سجلات الاستخدام: $usageCount\n";
        
        echo "\n🎉 نظام الكوبونات جاهز للاستخدام!\n";
        
    } catch (Exception $e) {
        echo "❌ خطأ في الملخص: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
    echo "\n💡 تأكد من:\n";
    echo "   - تشغيل خادم MySQL\n";
    echo "   - صحة بيانات الاتصال في config/database.php\n";
    echo "   - وجود قاعدة البيانات " . (defined('DB_NAME') ? DB_NAME : 'c7c_wolves7c') . "\n";
    echo "   - صلاحيات المستخدم للوصول لقاعدة البيانات\n";
}

echo "\n✅ انتهى الاختبار النهائي\n";
?>
