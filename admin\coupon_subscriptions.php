<?php
/**
 * إدارة تكامل الكوبونات مع الاشتراكات
 * Coupon Subscription Integration Management
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/layout.php';
require_once __DIR__ . '/../includes/CouponSubscriptionIntegration.php';

require_roles(['admin', 'super_admin']);

$pdo = getDBConnection();
$integration = new CouponSubscriptionIntegration($pdo, null, null);

// الحصول على الإحصائيات
$stats = [
    'total_subscriptions' => 0,
    'subscriptions_with_coupons' => 0,
    'total_discount_amount' => 0,
    'average_discount' => 0
];

try {
    // إجمالي الاشتراكات
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM player_subscriptions");
    $stats['total_subscriptions'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // الاشتراكات التي تحتوي على كوبونات
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM player_subscriptions WHERE coupon_id IS NOT NULL");
    $stats['subscriptions_with_coupons'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // إجمالي مبلغ الخصم
    $stmt = $pdo->query("SELECT SUM(coupon_discount_amount) as total FROM player_subscriptions WHERE coupon_id IS NOT NULL");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['total_discount_amount'] = $result['total'] ?? 0;
    
    // متوسط الخصم
    if ($stats['subscriptions_with_coupons'] > 0) {
        $stats['average_discount'] = $stats['total_discount_amount'] / $stats['subscriptions_with_coupons'];
    }
} catch (Exception $e) {}

// الحصول على الاشتراكات مع الكوبونات
$subscriptions = [];
try {
    $stmt = $pdo->query("
        SELECT 
            ps.id,
            ps.player_id,
            ps.plan_id,
            ps.coupon_code,
            ps.coupon_discount_amount,
            ps.final_amount,
            ps.status,
            ps.created_at,
            sp.name as plan_name,
            c.code as coupon_code_full
        FROM player_subscriptions ps
        LEFT JOIN subscription_plans sp ON ps.plan_id = sp.id
        LEFT JOIN coupons c ON ps.coupon_id = c.id
        WHERE ps.coupon_id IS NOT NULL
        ORDER BY ps.created_at DESC
        LIMIT 50
    ");
    $subscriptions = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تكامل الكوبونات مع الاشتراكات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f7fa; }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header { margin-bottom: 30px; }
        .header h1 { color: #2c3e50; font-size: 28px; margin-bottom: 10px; }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-right: 4px solid #3498db;
        }
        
        .stat-label { color: #7f8c8d; font-size: 12px; text-transform: uppercase; margin-bottom: 8px; }
        .stat-value { font-size: 32px; font-weight: bold; color: #2c3e50; }
        
        .table-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow-x: auto;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th {
            background: #ecf0f1;
            padding: 12px;
            text-align: right;
            color: #2c3e50;
            font-weight: 600;
            border-bottom: 2px solid #bdc3c7;
        }
        
        .table td {
            padding: 12px;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .table tr:hover {
            background: #f9f9f9;
        }
        
        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .badge-success { background: #d4edda; color: #155724; }
        .badge-warning { background: #fff3cd; color: #856404; }
        .badge-danger { background: #f8d7da; color: #721c24; }
        
        .actions-bar {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s;
        }
        
        .btn-primary { background: #3498db; color: white; }
        .btn-primary:hover { background: #2980b9; }
        .btn-export { background: #27ae60; color: white; }
        .btn-export:hover { background: #229954; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-link"></i> تكامل الكوبونات مع الاشتراكات</h1>
        </div>
        
        <!-- الإحصائيات -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-label">إجمالي الاشتراكات</div>
                <div class="stat-value"><?php echo $stats['total_subscriptions']; ?></div>
            </div>
            
            <div class="stat-card">
                <div class="stat-label">الاشتراكات مع كوبونات</div>
                <div class="stat-value"><?php echo $stats['subscriptions_with_coupons']; ?></div>
            </div>
            
            <div class="stat-card">
                <div class="stat-label">إجمالي الخصم</div>
                <div class="stat-value"><?php echo number_format($stats['total_discount_amount'], 2); ?> ريال</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-label">متوسط الخصم</div>
                <div class="stat-value"><?php echo number_format($stats['average_discount'], 2); ?> ريال</div>
            </div>
        </div>
        
        <!-- شريط الإجراءات -->
        <div class="actions-bar">
            <button class="btn btn-export" onclick="exportData()">
                <i class="fas fa-download"></i> تصدير البيانات
            </button>
            <a href="coupon_dashboard.php" class="btn btn-primary">
                <i class="fas fa-arrow-left"></i> العودة
            </a>
        </div>
        
        <!-- جدول الاشتراكات -->
        <div class="table-container">
            <h3 style="margin-bottom: 20px; color: #2c3e50;">
                <i class="fas fa-list"></i> آخر الاشتراكات مع الكوبونات
            </h3>
            
            <?php if (empty($subscriptions)): ?>
                <p style="color: #7f8c8d; text-align: center; padding: 20px;">
                    لا توجد اشتراكات مع كوبونات حتى الآن
                </p>
            <?php else: ?>
                <table class="table">
                    <thead>
                        <tr>
                            <th>معرف الاشتراك</th>
                            <th>معرف اللاعب</th>
                            <th>الخطة</th>
                            <th>كود الكوبون</th>
                            <th>مبلغ الخصم</th>
                            <th>المبلغ النهائي</th>
                            <th>الحالة</th>
                            <th>التاريخ</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($subscriptions as $sub): ?>
                            <tr>
                                <td><?php echo $sub['id']; ?></td>
                                <td><?php echo $sub['player_id']; ?></td>
                                <td><?php echo $sub['plan_name'] ?? 'N/A'; ?></td>
                                <td>
                                    <span class="badge badge-success">
                                        <?php echo $sub['coupon_code_full'] ?? $sub['coupon_code']; ?>
                                    </span>
                                </td>
                                <td><?php echo number_format($sub['coupon_discount_amount'], 2); ?> ريال</td>
                                <td><?php echo number_format($sub['final_amount'], 2); ?> ريال</td>
                                <td>
                                    <span class="badge badge-<?php echo $sub['status'] === 'active' ? 'success' : 'warning'; ?>">
                                        <?php echo $sub['status']; ?>
                                    </span>
                                </td>
                                <td><?php echo date('Y-m-d H:i', strtotime($sub['created_at'])); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        function exportData() {
            alert('سيتم تصدير البيانات قريباً');
        }
    </script>
</body>
</html>

