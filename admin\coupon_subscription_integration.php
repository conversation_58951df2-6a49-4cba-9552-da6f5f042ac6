<?php
/**
 * واجهة إدارة تكامل الكوبونات مع الاشتراكات
 * Admin Interface - Coupon Subscription Integration
 */

session_start();
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/CouponManager.php';
require_once __DIR__ . '/../includes/CouponValidator.php';
require_once __DIR__ . '/../includes/CouponSubscriptionIntegration.php';

// التحقق من المصادقة والصلاحيات
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role'] ?? '', ['admin', 'super_admin'])) {
    header('Location: ../login.php');
    exit;
}

$pdo = getDBConnection();
$couponManager = new CouponManager($pdo);
$couponValidator = new CouponValidator($pdo, $couponManager);
$subscriptionIntegration = new CouponSubscriptionIntegration($pdo, $couponManager, $couponValidator);

$message = '';
$messageType = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        if ($action === 'apply_to_subscription') {
            $subscriptionId = (int)($_POST['subscription_id'] ?? 0);
            $couponCode = $_POST['coupon_code'] ?? '';
            
            if (!$subscriptionId || !$couponCode) {
                throw new Exception('معرف الاشتراك وكود الكوبون مطلوبان');
            }
            
            $result = $subscriptionIntegration->applyToRenewal($subscriptionId, $couponCode);
            
            if ($result['success']) {
                $message = '✅ تم تطبيق الكوبون بنجاح - الخصم: ' . $result['discount_amount'];
                $messageType = 'success';
            } else {
                $message = '❌ خطأ: ' . $result['error'];
                $messageType = 'danger';
            }
        } elseif ($action === 'remove_coupon') {
            $subscriptionId = (int)($_POST['subscription_id'] ?? 0);
            
            if (!$subscriptionId) {
                throw new Exception('معرف الاشتراك مطلوب');
            }
            
            $result = $subscriptionIntegration->removeCoupon($subscriptionId);
            
            if ($result['success']) {
                $message = '✅ تم إزالة الكوبون بنجاح';
                $messageType = 'success';
            } else {
                $message = '❌ خطأ: ' . $result['error'];
                $messageType = 'danger';
            }
        }
    } catch (Exception $e) {
        $message = '❌ خطأ: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// الحصول على الاشتراكات النشطة
$stmt = $pdo->query("
    SELECT ps.*, sp.name as plan_name, sp.name_ar as plan_name_ar, sp.price as plan_price
    FROM player_subscriptions ps
    LEFT JOIN subscription_plans sp ON ps.plan_id = sp.id
    WHERE ps.status = 'active'
    ORDER BY ps.created_at DESC
    LIMIT 50
");
$subscriptions = $stmt->fetchAll(PDO::FETCH_ASSOC);

// الحصول على الكوبونات النشطة
$stmt = $pdo->query("
    SELECT id, code, discount_type, discount_value, valid_from, valid_until
    FROM coupons
    WHERE status = 'active' AND valid_until > NOW()
    ORDER BY code
");
$coupons = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة تكامل الكوبونات مع الاشتراكات</title>
    <link rel="stylesheet" href="../assets/css/bootstrap.min.css">
    <style>
        body { background: #f5f5f5; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .container { max-width: 1200px; margin: 20px auto; }
        .card { border: none; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .card-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .btn-primary { background: #667eea; border: none; }
        .btn-primary:hover { background: #764ba2; }
        .table { background: white; }
        .badge-success { background: #28a745; }
        .badge-danger { background: #dc3545; }
        .form-section { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .alert { border-radius: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">🎟️ إدارة تكامل الكوبونات مع الاشتراكات</h1>
        
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <div class="row">
            <div class="col-md-6">
                <div class="form-section">
                    <h3>📋 تطبيق الكوبون على الاشتراك</h3>
                    <form method="POST">
                        <input type="hidden" name="action" value="apply_to_subscription">
                        
                        <div class="mb-3">
                            <label class="form-label">اختر الاشتراك</label>
                            <select name="subscription_id" class="form-control" required>
                                <option value="">-- اختر اشتراك --</option>
                                <?php foreach ($subscriptions as $sub): ?>
                                    <option value="<?php echo $sub['id']; ?>">
                                        #<?php echo $sub['id']; ?> - <?php echo $sub['plan_name_ar'] ?? $sub['plan_name']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">اختر الكوبون</label>
                            <select name="coupon_code" class="form-control" required>
                                <option value="">-- اختر كوبون --</option>
                                <?php foreach ($coupons as $coupon): ?>
                                    <option value="<?php echo $coupon['code']; ?>">
                                        <?php echo $coupon['code']; ?> (<?php echo $coupon['discount_value']; ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100">
                            ✅ تطبيق الكوبون
                        </button>
                    </form>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="form-section">
                    <h3>🗑️ إزالة الكوبون</h3>
                    <form method="POST">
                        <input type="hidden" name="action" value="remove_coupon">
                        
                        <div class="mb-3">
                            <label class="form-label">اختر الاشتراك</label>
                            <select name="subscription_id" class="form-control" required>
                                <option value="">-- اختر اشتراك --</option>
                                <?php foreach ($subscriptions as $sub): ?>
                                    <?php if ($sub['coupon_id']): ?>
                                        <option value="<?php echo $sub['id']; ?>">
                                            #<?php echo $sub['id']; ?> - <?php echo $sub['coupon_code']; ?>
                                        </option>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-danger w-100">
                            🗑️ إزالة الكوبون
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h3>📊 الاشتراكات النشطة</h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>#</th>
                                <th>الخطة</th>
                                <th>السعر الأصلي</th>
                                <th>الكوبون</th>
                                <th>الخصم</th>
                                <th>السعر النهائي</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($subscriptions as $sub): ?>
                                <tr>
                                    <td>#<?php echo $sub['id']; ?></td>
                                    <td><?php echo $sub['plan_name_ar'] ?? $sub['plan_name']; ?></td>
                                    <td><?php echo number_format($sub['original_amount'] ?? $sub['plan_price'], 2); ?> ر.س</td>
                                    <td>
                                        <?php if ($sub['coupon_code']): ?>
                                            <span class="badge bg-success"><?php echo $sub['coupon_code']; ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">بدون</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo number_format($sub['coupon_discount_amount'] ?? 0, 2); ?> ر.س</td>
                                    <td><?php echo number_format($sub['final_amount'] ?? $sub['plan_price'], 2); ?> ر.س</td>
                                    <td>
                                        <span class="badge bg-info"><?php echo $sub['status']; ?></span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <script src="../assets/js/bootstrap.bundle.min.js"></script>
</body>
</html>

