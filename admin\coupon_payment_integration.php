<?php
/**
 * إدارة تكامل الكوبونات مع نظام الدفع
 * Coupon Payment Integration Management
 */

session_start();
require_once '../config.php';
require_once '../includes/CouponManager.php';
require_once '../includes/CouponValidator.php';
require_once '../includes/CouponPaymentIntegration.php';

// التحقق من الصلاحيات
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

$couponManager = new CouponManager($pdo);
$couponValidator = new CouponValidator($pdo, $couponManager);
$paymentIntegration = new CouponPaymentIntegration($pdo, $couponManager, $couponValidator);

$message = '';
$error = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        switch ($action) {
            case 'apply_coupon_to_invoice':
                $result = $paymentIntegration->applyToInvoice(
                    (int)$_POST['invoice_id'],
                    $_POST['coupon_code'],
                    (int)$_SESSION['user_id']
                );
                
                if ($result['success']) {
                    $message = 'تم تطبيق الكوبون على الفاتورة بنجاح';
                } else {
                    $error = $result['error'];
                }
                break;
                
            case 'apply_coupon_to_payment':
                $result = $paymentIntegration->applyToPayment(
                    (int)$_POST['payment_id'],
                    $_POST['coupon_code'],
                    (int)$_SESSION['user_id']
                );
                
                if ($result['success']) {
                    $message = 'تم تطبيق الكوبون على الدفعة بنجاح';
                } else {
                    $error = $result['error'];
                }
                break;
                
            case 'remove_coupon':
                $result = $paymentIntegration->removeCouponFromPayment(
                    (int)$_POST['payment_id']
                );
                
                if ($result['success']) {
                    $message = 'تم إلغاء الكوبون بنجاح';
                } else {
                    $error = $result['error'];
                }
                break;
        }
    } catch (Exception $e) {
        $error = 'خطأ: ' . $e->getMessage();
    }
}

// الحصول على الفواتير
$invoices = $pdo->query("
    SELECT id, invoice_number, total, coupon_code, coupon_discount_amount 
    FROM invoices 
    ORDER BY id DESC 
    LIMIT 20
")->fetchAll(PDO::FETCH_ASSOC);

// الحصول على المدفوعات
$payments = $pdo->query("
    SELECT id, payment_number, amount, coupon_code, discount_amount 
    FROM payments 
    ORDER BY id DESC 
    LIMIT 20
")->fetchAll(PDO::FETCH_ASSOC);

// الحصول على الكوبونات النشطة
$coupons = $couponManager->getActiveCoupons();
?>

<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تكامل الكوبونات مع الدفع</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .card { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .alert { padding: 15px; border-radius: 4px; margin-bottom: 20px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px; text-align: right; border-bottom: 1px solid #ddd; }
        th { background: #f8f9fa; font-weight: bold; }
        .btn { padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-success { background: #28a745; color: white; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎟️ تكامل الكوبونات مع نظام الدفع</h1>
            <p>إدارة تطبيق الكوبونات على الفواتير والمدفوعات</p>
        </div>
        
        <?php if ($message): ?>
            <div class="alert alert-success">✅ <?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="alert alert-danger">❌ <?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <div class="grid">
            <!-- تطبيق الكوبون على الفاتورة -->
            <div class="card">
                <h2>تطبيق الكوبون على الفاتورة</h2>
                <form method="POST">
                    <input type="hidden" name="action" value="apply_coupon_to_invoice">
                    
                    <div class="form-group">
                        <label>الفاتورة:</label>
                        <select name="invoice_id" required>
                            <option value="">اختر فاتورة</option>
                            <?php foreach ($invoices as $inv): ?>
                                <option value="<?php echo $inv['id']; ?>">
                                    <?php echo $inv['invoice_number'] . ' - ' . $inv['total'] . ' ريال'; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>الكوبون:</label>
                        <select name="coupon_code" required>
                            <option value="">اختر كوبون</option>
                            <?php foreach ($coupons as $coupon): ?>
                                <option value="<?php echo $coupon['code']; ?>">
                                    <?php echo $coupon['code'] . ' - ' . $coupon['name']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-success">تطبيق الكوبون</button>
                </form>
            </div>
            
            <!-- تطبيق الكوبون على الدفعة -->
            <div class="card">
                <h2>تطبيق الكوبون على الدفعة</h2>
                <form method="POST">
                    <input type="hidden" name="action" value="apply_coupon_to_payment">
                    
                    <div class="form-group">
                        <label>الدفعة:</label>
                        <select name="payment_id" required>
                            <option value="">اختر دفعة</option>
                            <?php foreach ($payments as $pay): ?>
                                <option value="<?php echo $pay['id']; ?>">
                                    <?php echo $pay['payment_number'] . ' - ' . $pay['amount'] . ' ريال'; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>الكوبون:</label>
                        <select name="coupon_code" required>
                            <option value="">اختر كوبون</option>
                            <?php foreach ($coupons as $coupon): ?>
                                <option value="<?php echo $coupon['code']; ?>">
                                    <?php echo $coupon['code'] . ' - ' . $coupon['name']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-success">تطبيق الكوبون</button>
                </form>
            </div>
        </div>
        
        <!-- الفواتير -->
        <div class="card">
            <h2>الفواتير</h2>
            <table>
                <thead>
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>المبلغ</th>
                        <th>الكوبون</th>
                        <th>الخصم</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($invoices as $inv): ?>
                        <tr>
                            <td><?php echo $inv['invoice_number']; ?></td>
                            <td><?php echo $inv['total']; ?> ريال</td>
                            <td><?php echo $inv['coupon_code'] ?? '-'; ?></td>
                            <td><?php echo $inv['coupon_discount_amount'] ?? 0; ?> ريال</td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- المدفوعات -->
        <div class="card">
            <h2>المدفوعات</h2>
            <table>
                <thead>
                    <tr>
                        <th>رقم الدفعة</th>
                        <th>المبلغ</th>
                        <th>الكوبون</th>
                        <th>الخصم</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($payments as $pay): ?>
                        <tr>
                            <td><?php echo $pay['payment_number']; ?></td>
                            <td><?php echo $pay['amount']; ?> ريال</td>
                            <td><?php echo $pay['coupon_code'] ?? '-'; ?></td>
                            <td><?php echo $pay['discount_amount'] ?? 0; ?> ريال</td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>

