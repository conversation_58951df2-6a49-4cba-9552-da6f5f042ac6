-- =====================================================
-- إنشاء جدول الكوبونات وإدراج كوبون تجريبي
-- Create Coupons Table and Insert Test Coupon
-- =====================================================

-- إنشاء جدول الكوبونات أولاً
CREATE TABLE IF NOT EXISTS `coupons` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `uuid` CHAR(36) UNIQUE NOT NULL,
  `code` VARCHAR(50) UNIQUE NOT NULL COMMENT 'كود الكوبون الفريد',
  `name` VARCHAR(100) NOT NULL COMMENT 'اسم الكوبون',
  `description` TEXT NULL COMMENT 'وصف الكوبون',
  `type` ENUM('percentage','fixed','free_month','free_session','upgrade') NOT NULL DEFAULT 'percentage' COMMENT 'نوع الخصم',
  `value` DECIMAL(10,2) NOT NULL COMMENT 'قيمة الخصم',
  `minimum_amount` DECIMAL(10,2) DEFAULT 0 COMMENT 'الحد الأدنى للمبلغ',
  `maximum_discount` DECIMAL(10,2) NULL COMMENT 'أقصى خصم',
  `usage_limit` INT UNSIGNED NULL COMMENT 'حد الاستخدام الكلي',
  `usage_limit_per_user` TINYINT UNSIGNED DEFAULT 1 COMMENT 'حد الاستخدام لكل مستخدم',
  `used_count` INT UNSIGNED DEFAULT 0 COMMENT 'عدد مرات الاستخدام',
  `valid_from` DATETIME NOT NULL COMMENT 'تاريخ البداية',
  `valid_until` DATETIME NOT NULL COMMENT 'تاريخ النهاية',
  `is_active` BOOLEAN DEFAULT TRUE COMMENT 'هل الكوبون مفعل',
  `applicable_to` JSON NULL COMMENT 'قابل للتطبيق على (plans, services, etc)',
  `user_restrictions` JSON NULL COMMENT 'قيود المستخدمين (new_members, vip, etc)',
  `excluded_users` JSON NULL COMMENT 'المستخدمون المستثنون',
  `created_by` BIGINT UNSIGNED NULL COMMENT 'من أنشأ الكوبون',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_code` (`code`),
  INDEX `idx_is_active` (`is_active`),
  INDEX `idx_valid_dates` (`valid_from`, `valid_until`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الكوبونات الرئيسي';

-- إدراج كوبون تجريبي
INSERT INTO `coupons` (
    `uuid`,
    `code`,
    `name`,
    `description`,
    `type`,
    `value`,
    `minimum_amount`,
    `maximum_discount`,
    `usage_limit`,
    `usage_limit_per_user`,
    `used_count`,
    `valid_from`,
    `valid_until`,
    `is_active`,
    `applicable_to`,
    `user_restrictions`,
    `excluded_users`,
    `created_by`
) VALUES (
    UUID(),
    'TEST2025',
    'كوبون اختبار 2025',
    'كوبون تجريبي للاختبار',
    'percentage',
    10.00,
    50.00,
    100.00,
    100,
    1,
    0,
    '2025-01-01 00:00:00',
    '2025-12-31 23:59:59',
    1,
    '["all"]',
    '[]',
    '[]',
    1
) ON DUPLICATE KEY UPDATE
    `name` = VALUES(`name`),
    `description` = VALUES(`description`),
    `updated_at` = NOW();

-- رسالة نجاح
SELECT 'تم إنشاء الجدول وإدراج الكوبون التجريبي بنجاح!' AS message;
