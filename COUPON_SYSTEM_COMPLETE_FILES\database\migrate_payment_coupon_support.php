<?php
/**
 * سكريبت الترحيل - إضافة دعم الكوبونات لجداول الدفع والفواتير
 * Migration Script - Add Coupon Support to Payment and Invoice Tables
 */

require_once '../config.php';

echo "🔄 جاري ترحيل قاعدة البيانات...\n";
echo "🔄 Migrating database...\n\n";

try {
    // 1. تحديث جدول invoices
    echo "1️⃣ تحديث جدول الفواتير (invoices)...\n";
    
    $migrations = [
        // إضافة أعمدة الكوبونات للفواتير
        "ALTER TABLE `invoices` ADD COLUMN IF NOT EXISTS `coupon_id` BIGINT UNSIGNED NULL COMMENT 'معرف الكوبون المطبق'",
        "ALTER TABLE `invoices` ADD COLUMN IF NOT EXISTS `coupon_code` VARCHAR(50) NULL COMMENT 'كود الكوبون'",
        "ALTER TABLE `invoices` ADD COLUMN IF NOT EXISTS `coupon_discount_amount` DECIMAL(10,2) DEFAULT 0 COMMENT 'مبلغ خصم الكوبون'",
        "ALTER TABLE `invoices` ADD COLUMN IF NOT EXISTS `final_amount` DECIMAL(10,2) NULL COMMENT 'المبلغ النهائي بعد الخصم'",
        
        // إضافة الفهارس
        "ALTER TABLE `invoices` ADD INDEX IF NOT EXISTS `idx_coupon_id` (`coupon_id`)",
        "ALTER TABLE `invoices` ADD INDEX IF NOT EXISTS `idx_coupon_code` (`coupon_code`)",
        
        // 2. تحديث جدول payments
        "ALTER TABLE `payments` ADD COLUMN IF NOT EXISTS `coupon_id` BIGINT UNSIGNED NULL COMMENT 'معرف الكوبون المطبق'",
        "ALTER TABLE `payments` ADD COLUMN IF NOT EXISTS `coupon_code` VARCHAR(50) NULL COMMENT 'كود الكوبون'",
        "ALTER TABLE `payments` ADD COLUMN IF NOT EXISTS `discount_amount` DECIMAL(10,2) DEFAULT 0 COMMENT 'مبلغ الخصم'",
        "ALTER TABLE `payments` ADD COLUMN IF NOT EXISTS `final_amount` DECIMAL(10,2) NULL COMMENT 'المبلغ النهائي بعد الخصم'",
        "ALTER TABLE `payments` ADD COLUMN IF NOT EXISTS `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'آخر تحديث'",
        
        // إضافة الفهارس
        "ALTER TABLE `payments` ADD INDEX IF NOT EXISTS `idx_coupon_id` (`coupon_id`)",
        "ALTER TABLE `payments` ADD INDEX IF NOT EXISTS `idx_coupon_code` (`coupon_code`)"
    ];
    
    foreach ($migrations as $sql) {
        try {
            $pdo->exec($sql);
            echo "   ✅ " . substr($sql, 0, 60) . "...\n";
        } catch (Exception $e) {
            echo "   ⚠️  " . substr($sql, 0, 60) . "... (قد يكون موجوداً بالفعل)\n";
        }
    }
    
    // 3. إضافة Foreign Keys
    echo "\n2️⃣ إضافة العلاقات (Foreign Keys)...\n";
    
    $foreignKeys = [
        "ALTER TABLE `invoices` ADD CONSTRAINT `fk_invoices_coupon` FOREIGN KEY (`coupon_id`) REFERENCES `coupons`(`id`) ON DELETE SET NULL",
        "ALTER TABLE `payments` ADD CONSTRAINT `fk_payments_coupon` FOREIGN KEY (`coupon_id`) REFERENCES `coupons`(`id`) ON DELETE SET NULL"
    ];
    
    foreach ($foreignKeys as $sql) {
        try {
            $pdo->exec($sql);
            echo "   ✅ " . substr($sql, 0, 60) . "...\n";
        } catch (Exception $e) {
            echo "   ⚠️  " . substr($sql, 0, 60) . "... (قد يكون موجوداً بالفعل)\n";
        }
    }
    
    // 4. التحقق من النتائج
    echo "\n3️⃣ التحقق من النتائج...\n";
    
    // التحقق من أعمدة invoices
    $invoiceColumns = $pdo->query("DESCRIBE invoices")->fetchAll(PDO::FETCH_ASSOC);
    $invoiceColumnNames = array_column($invoiceColumns, 'Field');
    
    $requiredInvoiceColumns = ['coupon_id', 'coupon_code', 'coupon_discount_amount', 'final_amount'];
    foreach ($requiredInvoiceColumns as $col) {
        if (in_array($col, $invoiceColumnNames)) {
            echo "   ✅ جدول invoices - العمود $col موجود\n";
        } else {
            echo "   ❌ جدول invoices - العمود $col غير موجود\n";
        }
    }
    
    // التحقق من أعمدة payments
    $paymentColumns = $pdo->query("DESCRIBE payments")->fetchAll(PDO::FETCH_ASSOC);
    $paymentColumnNames = array_column($paymentColumns, 'Field');
    
    $requiredPaymentColumns = ['coupon_id', 'coupon_code', 'discount_amount', 'final_amount', 'updated_at'];
    foreach ($requiredPaymentColumns as $col) {
        if (in_array($col, $paymentColumnNames)) {
            echo "   ✅ جدول payments - العمود $col موجود\n";
        } else {
            echo "   ❌ جدول payments - العمود $col غير موجود\n";
        }
    }
    
    echo "\n✅ تم إكمال الترحيل بنجاح!\n";
    echo "✅ Migration completed successfully!\n";
    
} catch (Exception $e) {
    echo "\n❌ خطأ أثناء الترحيل:\n";
    echo "❌ Migration error:\n";
    echo $e->getMessage() . "\n";
    exit(1);
}
?>

