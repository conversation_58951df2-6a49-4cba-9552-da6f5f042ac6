# 📊 دليل تحديث قاعدة البيانات
# Database Update Guide

## 🎯 الهدف | Objective

تحديث قاعدة البيانات لإضافة دعم الكوبونات الكامل لنظام الاشتراكات.

---

## 📋 المحتويات | Contents

1. [الملفات المطلوبة](#الملفات-المطلوبة)
2. [خطوات التحديث](#خطوات-التحديث)
3. [التحقق من التحديث](#التحقق-من-التحديث)
4. [استكشاف الأخطاء](#استكشاف-الأخطاء)
5. [الملفات المُنشأة](#الملفات-المُنشأة)

---

## 📁 الملفات المطلوبة | Required Files

### ملفات التحديث
1. **database/update_subscription_database.php**
   - سكريبت تحديث قاعدة البيانات
   - يضيف أعمدة الكوبونات
   - ينشئ الفهارس
   - ينشئ العلاقات

2. **database/verify_subscription_database.php**
   - سكريبت التحقق من قاعدة البيانات
   - يتحقق من الأعمدة
   - يتحقق من الفهارس
   - يتحقق من العلاقات

### ملفات الترحيل السابقة
1. **database/migrate_subscription_coupon_support.php**
   - ترحيل سابق (موجود)
   - يضيف أعمدة الكوبونات

2. **database/migrate_payment_coupon_support.php**
   - ترحيل سابق (موجود)
   - يضيف أعمدة الكوبونات للفواتير والدفع

---

## 🚀 خطوات التحديث | Update Steps

### الخطوة 1: التحقق من الحالة الحالية
```bash
php database/verify_subscription_database.php
```

**النتيجة المتوقعة:**
- ✅ جميع الجداول موجودة
- ✅ جميع الأعمدة موجودة
- ✅ جميع الفهارس موجودة
- ✅ جميع العلاقات موجودة

### الخطوة 2: تشغيل سكريبت التحديث
```bash
php database/update_subscription_database.php
```

**النتيجة المتوقعة:**
- ✅ تم إضافة 7 أعمدة
- ✅ تم إضافة 3 فهارس
- ✅ تم إضافة 1 علاقة
- ✅ إجمالي 11 تحديث

### الخطوة 3: التحقق من التحديث
```bash
php database/verify_subscription_database.php
```

**النتيجة المتوقعة:**
- ✅ قاعدة البيانات جاهزة للاستخدام

---

## ✅ التحقق من التحديث | Verification

### الأعمدة المضافة (7 أعمدة)
| العمود | النوع | الوصف |
|--------|-------|-------|
| coupon_id | BIGINT UNSIGNED | معرف الكوبون |
| coupon_code | VARCHAR(50) | كود الكوبون |
| original_amount | DECIMAL(10,2) | المبلغ الأصلي |
| coupon_discount_amount | DECIMAL(10,2) | مبلغ الخصم |
| final_amount | DECIMAL(10,2) | المبلغ النهائي |
| coupon_applied_at | TIMESTAMP | وقت التطبيق |
| coupon_applied_by | INT UNSIGNED | من طبق الكوبون |

### الفهارس المضافة (3 فهارس)
| الفهرس | الأعمدة | الغرض |
|--------|--------|-------|
| idx_coupon_id | coupon_id | البحث السريع عن الكوبونات |
| idx_coupon_code | coupon_code | البحث السريع عن أكواد الكوبونات |
| idx_coupon_applied | coupon_applied_at | البحث السريع عن التطبيقات |

### العلاقات المضافة (1 علاقة)
| العلاقة | الجدول | الحقل | المرجع |
|--------|--------|-------|--------|
| fk_subscription_coupon | player_subscriptions | coupon_id | coupons.id |

---

## 🔧 استكشاف الأخطاء | Troubleshooting

### المشكلة: "جدول غير موجود"
**الحل:**
```bash
php api/subscription.php
```
سيقوم بإنشاء الجداول تلقائياً

### المشكلة: "عمود موجود بالفعل"
**الحل:**
هذا تحذير عادي - الأعمدة موجودة بالفعل

### المشكلة: "خطأ في الاتصال"
**الحل:**
تحقق من ملف config.php والاتصال بقاعدة البيانات

---

## 📊 الملفات المُنشأة | Created Files

### ملفات التحديث
1. ✅ `database/update_subscription_database.php` (150 سطر)
2. ✅ `database/verify_subscription_database.php` (150 سطر)
3. ✅ `docs/DATABASE_UPDATE_GUIDE.md` (150 سطر)

### ملفات الترحيل السابقة (موجودة)
1. ✅ `database/migrate_subscription_coupon_support.php`
2. ✅ `database/migrate_payment_coupon_support.php`

---

## 🎯 الخطوات التالية | Next Steps

### بعد التحديث
1. ✅ تشغيل سكريبت التحديث
2. ✅ التحقق من النتائج
3. ⏳ تطوير واجهات API
4. ⏳ تطوير واجهة الإدارة
5. ⏳ كتابة الاختبارات

---

## 📝 ملاحظات مهمة | Important Notes

1. **النسخ الاحتياطية**: تأكد من عمل نسخة احتياطية قبل التحديث
2. **الاختبار**: اختبر في بيئة التطوير أولاً
3. **التوثيق**: احتفظ بسجل التحديثات
4. **الأداء**: الفهارس تحسن الأداء بشكل كبير

---

**تاريخ الإنشاء:** 2025-12-28
**الإصدار:** 1.0
**الحالة:** ✅ جاهز للاستخدام

