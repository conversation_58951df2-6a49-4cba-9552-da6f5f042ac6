<?php
/**
 * تكامل الكوبونات مع نظام الدفع
 * Coupon Payment Integration API
 * 
 * يوفر واجهات برمجية لتطبيق الكوبونات على الدفعات والفواتير
 */

header('Content-Type: application/json; charset=utf-8');

require_once '../config.php';
require_once '../includes/CouponManager.php';
require_once '../includes/CouponValidator.php';
require_once '../includes/CouponPaymentIntegration.php';

try {
    $action = $_GET['action'] ?? $_POST['action'] ?? null;
    
    // تهيئة الفئات
    $couponManager = new CouponManager($pdo);
    $couponValidator = new CouponValidator($pdo, $couponManager);
    $paymentIntegration = new CouponPaymentIntegration($pdo, $couponManager, $couponValidator);
    
    switch ($action) {
        case 'process_payment_with_coupon':
            // معالجة الدفع مع الكوبون
            $result = $paymentIntegration->processPaymentWithCoupon([
                'coupon_code' => $_POST['coupon_code'] ?? null,
                'amount' => (float)($_POST['amount'] ?? 0),
                'user_id' => (int)($_POST['user_id'] ?? 0),
                'plan_id' => (int)($_POST['plan_id'] ?? 0),
                'subscription_id' => (int)($_POST['subscription_id'] ?? 0),
                'payment_id' => (int)($_POST['payment_id'] ?? 0)
            ]);
            echo json_encode($result);
            break;
            
        case 'apply_to_invoice':
            // تطبيق الكوبون على الفاتورة
            $result = $paymentIntegration->applyToInvoice(
                (int)$_POST['invoice_id'],
                $_POST['coupon_code'],
                (int)$_POST['user_id']
            );
            echo json_encode($result);
            break;
            
        case 'apply_to_payment':
            // تطبيق الكوبون على الدفعة
            $result = $paymentIntegration->applyToPayment(
                (int)$_POST['payment_id'],
                $_POST['coupon_code'],
                (int)$_POST['user_id']
            );
            echo json_encode($result);
            break;
            
        case 'remove_coupon':
            // إلغاء الكوبون من الدفعة
            $result = $paymentIntegration->removeCouponFromPayment(
                (int)$_POST['payment_id']
            );
            echo json_encode($result);
            break;
            
        case 'validate_coupon_for_payment':
            // التحقق من صحة الكوبون للدفع
            $validation = $couponValidator->validate(
                $_POST['coupon_code'],
                [
                    'amount' => (float)($_POST['amount'] ?? 0),
                    'user_id' => (int)($_POST['user_id'] ?? 0),
                    'plan_id' => (int)($_POST['plan_id'] ?? 0)
                ]
            );
            echo json_encode($validation);
            break;
            
        case 'calculate_discount':
            // حساب الخصم
            $coupon = $couponManager->getCouponByCode($_POST['coupon_code']);
            if (!$coupon) {
                echo json_encode(['success' => false, 'error' => 'الكوبون غير موجود']);
                break;
            }
            
            $discount = $couponManager->calculateDiscount(
                $coupon,
                (float)($_POST['amount'] ?? 0)
            );
            
            echo json_encode([
                'success' => true,
                'discount_amount' => $discount,
                'final_amount' => (float)($_POST['amount'] ?? 0) - $discount
            ]);
            break;
            
        default:
            echo json_encode(['success' => false, 'error' => 'إجراء غير معروف']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>

