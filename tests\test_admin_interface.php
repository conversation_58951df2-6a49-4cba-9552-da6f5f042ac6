<?php
/**
 * اختبارات واجهة الإدارة
 * Admin Interface Tests
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/CouponManager.php';
require_once __DIR__ . '/../includes/CouponReportingSystem.php';

echo "🧪 بدء اختبارات واجهة الإدارة\n";
echo "=" . str_repeat("=", 80) . "\n\n";

try {
    $pdo = getDBConnection();
    $couponManager = new CouponManager($pdo);
    $reportingSystem = new CouponReportingSystem($pdo);
    
    $tests_passed = 0;
    $tests_failed = 0;
    
    // ============================================================================
    // 1. اختبار لوحة التحكم - الإحصائيات
    // ============================================================================
    echo "1️⃣ اختبار لوحة التحكم - الإحصائيات:\n";
    
    try {
        // إجمالي الكوبونات
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM coupons");
        $total = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "  ✅ إجمالي الكوبونات: $total\n";
        $tests_passed++;
        
        // الكوبونات النشطة
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM coupons WHERE is_active = 1");
        $active = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "  ✅ الكوبونات النشطة: $active\n";
        $tests_passed++;
        
        // الكوبونات المنتهية
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM coupons WHERE expires_at < NOW()");
        $expired = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "  ✅ الكوبونات المنتهية: $expired\n";
        $tests_passed++;
        
        // إجمالي الاستخدام
        $stmt = $pdo->query("SELECT SUM(usage_count) as total FROM coupons");
        $usage = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;
        echo "  ✅ إجمالي الاستخدام: $usage\n";
        $tests_passed++;
        
    } catch (Exception $e) {
        echo "  ❌ خطأ في الإحصائيات: " . $e->getMessage() . "\n";
        $tests_failed++;
    }
    
    // ============================================================================
    // 2. اختبار إنشاء كوبون
    // ============================================================================
    echo "\n2️⃣ اختبار إنشاء كوبون:\n";
    
    try {
        $testCode = 'TEST' . time();
        $result = $couponManager->createCoupon([
            'code' => $testCode,
            'type' => 'percent',
            'value' => 15,
            'description' => 'كوبون اختبار',
            'is_active' => 1
        ]);
        
        if ($result['success']) {
            echo "  ✅ تم إنشاء الكوبون بنجاح: $testCode\n";
            $tests_passed++;
        } else {
            echo "  ❌ فشل إنشاء الكوبون\n";
            $tests_failed++;
        }
    } catch (Exception $e) {
        echo "  ❌ خطأ: " . $e->getMessage() . "\n";
        $tests_failed++;
    }
    
    // ============================================================================
    // 3. اختبار التقارير
    // ============================================================================
    echo "\n3️⃣ اختبار التقارير:\n";
    
    try {
        $usage_report = $reportingSystem->getUsageReport();
        echo "  ✅ تقرير الاستخدام: " . json_encode($usage_report) . "\n";
        $tests_passed++;
        
        $performance_report = $reportingSystem->getPerformanceReport();
        echo "  ✅ تقرير الأداء: " . json_encode($performance_report) . "\n";
        $tests_passed++;
        
        $revenue_report = $reportingSystem->getRevenueReport();
        echo "  ✅ تقرير الإيرادات: " . json_encode($revenue_report) . "\n";
        $tests_passed++;
        
    } catch (Exception $e) {
        echo "  ❌ خطأ في التقارير: " . $e->getMessage() . "\n";
        $tests_failed++;
    }
    
    // ============================================================================
    // 4. اختبار تكامل الاشتراكات
    // ============================================================================
    echo "\n4️⃣ اختبار تكامل الاشتراكات:\n";
    
    try {
        // الاشتراكات مع الكوبونات
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM player_subscriptions WHERE coupon_id IS NOT NULL");
        $with_coupons = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "  ✅ الاشتراكات مع كوبونات: $with_coupons\n";
        $tests_passed++;
        
        // إجمالي الخصم
        $stmt = $pdo->query("SELECT SUM(coupon_discount_amount) as total FROM player_subscriptions WHERE coupon_id IS NOT NULL");
        $total_discount = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;
        echo "  ✅ إجمالي الخصم: $total_discount\n";
        $tests_passed++;
        
    } catch (Exception $e) {
        echo "  ❌ خطأ في تكامل الاشتراكات: " . $e->getMessage() . "\n";
        $tests_failed++;
    }
    
    // ============================================================================
    // النتائج النهائية
    // ============================================================================
    echo "\n" . str_repeat("=", 80) . "\n";
    echo "📊 النتائج النهائية:\n";
    echo "  ✅ نجح: $tests_passed\n";
    echo "  ❌ فشل: $tests_failed\n";
    echo "  📈 النسبة: " . round(($tests_passed / ($tests_passed + $tests_failed)) * 100, 2) . "%\n";
    
} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
}

echo "\n✅ انتهت الاختبارات\n";
?>

