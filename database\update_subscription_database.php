<?php
/**
 * سكريبت تحديث قاعدة البيانات - نظام الاشتراكات مع الكوبونات
 * Database Update Script - Subscription System with Coupons
 */

require_once __DIR__ . '/../config.php';

echo "🔄 بدء تحديث قاعدة البيانات - نظام الاشتراكات مع الكوبونات\n";
echo "=" . str_repeat("=", 80) . "\n\n";

try {
    $pdo = getDBConnection();
    
    // ============================================================================
    // 1. التحقق من وجود جداول الاشتراكات
    // ============================================================================
    echo "1️⃣ التحقق من جداول الاشتراكات...\n";
    
    $tables = $pdo->query("SHOW TABLES LIKE 'player_subscriptions'")->fetchAll();
    if (empty($tables)) {
        echo "  ⚠️  جدول player_subscriptions غير موجود - سيتم إنشاؤه\n";
        // سيتم إنشاؤه من قبل api/subscription.php
    } else {
        echo "  ✅ جدول player_subscriptions موجود\n";
    }
    
    $tables = $pdo->query("SHOW TABLES LIKE 'subscription_plans'")->fetchAll();
    if (empty($tables)) {
        echo "  ⚠️  جدول subscription_plans غير موجود - سيتم إنشاؤه\n";
    } else {
        echo "  ✅ جدول subscription_plans موجود\n";
    }
    
    // ============================================================================
    // 2. إضافة أعمدة الكوبونات لجدول player_subscriptions
    // ============================================================================
    echo "\n2️⃣ إضافة أعمدة الكوبونات لجدول player_subscriptions...\n";
    
    $couponColumns = [
        "ALTER TABLE `player_subscriptions` ADD COLUMN IF NOT EXISTS `coupon_id` BIGINT UNSIGNED NULL COMMENT 'معرف الكوبون'",
        "ALTER TABLE `player_subscriptions` ADD COLUMN IF NOT EXISTS `coupon_code` VARCHAR(50) NULL COMMENT 'كود الكوبون'",
        "ALTER TABLE `player_subscriptions` ADD COLUMN IF NOT EXISTS `original_amount` DECIMAL(10,2) DEFAULT 0 COMMENT 'المبلغ الأصلي قبل الخصم'",
        "ALTER TABLE `player_subscriptions` ADD COLUMN IF NOT EXISTS `coupon_discount_amount` DECIMAL(10,2) DEFAULT 0 COMMENT 'مبلغ الخصم من الكوبون'",
        "ALTER TABLE `player_subscriptions` ADD COLUMN IF NOT EXISTS `final_amount` DECIMAL(10,2) NULL COMMENT 'المبلغ النهائي بعد الخصم'",
        "ALTER TABLE `player_subscriptions` ADD COLUMN IF NOT EXISTS `coupon_applied_at` TIMESTAMP NULL COMMENT 'وقت تطبيق الكوبون'",
        "ALTER TABLE `player_subscriptions` ADD COLUMN IF NOT EXISTS `coupon_applied_by` INT UNSIGNED NULL COMMENT 'معرف المستخدم الذي طبق الكوبون'",
    ];
    
    foreach ($couponColumns as $sql) {
        try {
            $pdo->exec($sql);
            echo "  ✅ " . substr($sql, 0, 70) . "...\n";
        } catch (Exception $e) {
            echo "  ⚠️  " . substr($sql, 0, 70) . "... (قد يكون موجوداً)\n";
        }
    }
    
    // ============================================================================
    // 3. إضافة فهارس للأداء
    // ============================================================================
    echo "\n3️⃣ إضافة فهارس للأداء...\n";
    
    $indexes = [
        "ALTER TABLE `player_subscriptions` ADD INDEX IF NOT EXISTS `idx_coupon_id` (`coupon_id`)",
        "ALTER TABLE `player_subscriptions` ADD INDEX IF NOT EXISTS `idx_coupon_code` (`coupon_code`)",
        "ALTER TABLE `player_subscriptions` ADD INDEX IF NOT EXISTS `idx_coupon_applied` (`coupon_applied_at`)",
    ];
    
    foreach ($indexes as $sql) {
        try {
            $pdo->exec($sql);
            echo "  ✅ " . substr($sql, 0, 70) . "...\n";
        } catch (Exception $e) {
            echo "  ⚠️  " . substr($sql, 0, 70) . "... (قد يكون موجوداً)\n";
        }
    }
    
    // ============================================================================
    // 4. إضافة علاقات Foreign Key
    // ============================================================================
    echo "\n4️⃣ إضافة علاقات قاعدة البيانات...\n";
    
    try {
        $pdo->exec("ALTER TABLE `player_subscriptions` ADD CONSTRAINT `fk_subscription_coupon` 
                   FOREIGN KEY (`coupon_id`) REFERENCES `coupons` (`id`) 
                   ON DELETE SET NULL ON UPDATE CASCADE");
        echo "  ✅ تم إضافة علاقة Foreign Key للكوبونات\n";
    } catch (Exception $e) {
        echo "  ⚠️  علاقة Foreign Key قد تكون موجودة\n";
    }
    
    // ============================================================================
    // 5. التحقق من النتائج
    // ============================================================================
    echo "\n5️⃣ التحقق من النتائج...\n";
    
    $stmt = $pdo->query("DESCRIBE player_subscriptions");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $columnNames = array_column($columns, 'Field');
    
    $requiredColumns = [
        'coupon_id' => 'معرف الكوبون',
        'coupon_code' => 'كود الكوبون',
        'original_amount' => 'المبلغ الأصلي',
        'coupon_discount_amount' => 'مبلغ الخصم',
        'final_amount' => 'المبلغ النهائي',
        'coupon_applied_at' => 'وقت التطبيق',
        'coupon_applied_by' => 'من طبق الكوبون'
    ];
    
    $allPresent = true;
    foreach ($requiredColumns as $col => $desc) {
        if (in_array($col, $columnNames)) {
            echo "  ✅ العمود '$col' ($desc) موجود\n";
        } else {
            echo "  ❌ العمود '$col' ($desc) غير موجود\n";
            $allPresent = false;
        }
    }
    
    // ============================================================================
    // 6. إحصائيات النتائج
    // ============================================================================
    echo "\n" . str_repeat("=", 80) . "\n";
    echo "📊 ملخص التحديث:\n";
    echo "  • عدد الأعمدة المضافة: 7\n";
    echo "  • عدد الفهارس المضافة: 3\n";
    echo "  • عدد العلاقات المضافة: 1\n";
    echo "  • إجمالي التحديثات: 11\n";
    
    if ($allPresent) {
        echo "\n✅ تم إكمال التحديث بنجاح!\n";
        echo "✅ جميع الأعمدة والفهارس والعلاقات تم إضافتها بنجاح\n";
    } else {
        echo "\n⚠️  تم إكمال التحديث مع بعض التحذيرات\n";
    }
    
    echo "\n🎉 تحديث قاعدة البيانات اكتمل!\n";
    echo "=" . str_repeat("=", 80) . "\n";
    
} catch (Exception $e) {
    echo "\n❌ خطأ أثناء التحديث:\n";
    echo $e->getMessage() . "\n";
    exit(1);
}
?>

