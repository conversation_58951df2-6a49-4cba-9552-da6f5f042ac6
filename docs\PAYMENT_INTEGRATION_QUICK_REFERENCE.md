# مرجع سريع - تكامل الكوبونات مع الدفع
# Quick Reference - Coupon Payment Integration

## 🚀 البدء السريع

### 1. تشغيل الترحيل
```bash
php database/migrate_payment_coupon_support.php
```

### 2. استخدام الفئات
```php
require_once 'includes/CouponPaymentIntegration.php';

$integration = new CouponPaymentIntegration($pdo, $couponManager, $couponValidator);

// تطبيق الكوبون على الفاتورة
$result = $integration->applyToInvoice($invoiceId, 'COUPON_CODE', $userId);

// تطبيق الكوبون على الدفعة
$result = $integration->applyToPayment($paymentId, 'COUPON_CODE', $userId);
```

## 📡 استدعاءات API

### تطبيق الكوبون على الفاتورة
```bash
curl -X POST http://system.c7c.club/api/coupons_payment_integration.php \
  -d "action=apply_to_invoice" \
  -d "invoice_id=1" \
  -d "coupon_code=SUMMER2024" \
  -d "user_id=1"
```

### تطبيق الكوبون على الدفعة
```bash
curl -X POST http://system.c7c.club/api/coupons_payment_integration.php \
  -d "action=apply_to_payment" \
  -d "payment_id=1" \
  -d "coupon_code=SUMMER2024" \
  -d "user_id=1"
```

### حساب الخصم
```bash
curl -X POST http://system.c7c.club/api/coupons_payment_integration.php \
  -d "action=calculate_discount" \
  -d "coupon_code=SUMMER2024" \
  -d "amount=500"
```

### التحقق من صحة الكوبون
```bash
curl -X POST http://system.c7c.club/api/coupons_payment_integration.php \
  -d "action=validate_coupon_for_payment" \
  -d "coupon_code=SUMMER2024" \
  -d "amount=500" \
  -d "user_id=1"
```

## 💾 استعلامات قاعدة البيانات

### الحصول على الفواتير مع الكوبونات
```sql
SELECT id, invoice_number, total, coupon_code, coupon_discount_amount, final_amount
FROM invoices
WHERE coupon_id IS NOT NULL
ORDER BY id DESC;
```

### الحصول على المدفوعات مع الكوبونات
```sql
SELECT id, payment_number, amount, coupon_code, discount_amount, final_amount
FROM payments
WHERE coupon_id IS NOT NULL
ORDER BY id DESC;
```

### إحصائيات الكوبونات
```sql
SELECT 
  c.code,
  c.name,
  COUNT(cu.id) as usage_count,
  SUM(cu.discount_amount) as total_discount,
  SUM(cu.final_amount) as total_revenue
FROM coupons c
LEFT JOIN coupon_usage cu ON c.id = cu.coupon_id
GROUP BY c.id
ORDER BY usage_count DESC;
```

## 🔧 الدوال الرئيسية

### CouponPaymentIntegration

```php
// معالجة الدفع مع الكوبون
processPaymentWithCoupon(array $paymentData): array

// تطبيق الكوبون على الفاتورة
applyToInvoice(int $invoiceId, string $couponCode, int $userId): array

// تطبيق الكوبون على الدفعة
applyToPayment(int $paymentId, string $couponCode, int $userId): array

// إلغاء الكوبون من الدفعة
removeCouponFromPayment(int $paymentId): array

// الحصول على بيانات الفاتورة
getInvoice(int $invoiceId): ?array

// الحصول على بيانات الدفعة
getPayment(int $paymentId): ?array
```

## 📊 أنواع الخصم المدعومة

| النوع | الوصف | مثال |
|------|-------|------|
| percentage | خصم نسبة مئوية | 10% من المبلغ |
| fixed | خصم ثابت | 50 ريال |
| free_month | شهر مجاني | شهر اشتراك مجاني |
| free_session | جلسة مجانية | جلسة تدريب مجانية |
| upgrade | ترقية مجانية | ترقية الخطة |

## ⚠️ رموز الأخطاء

| الرمز | الوصف |
|------|-------|
| COUPON_NOT_FOUND | الكوبون غير موجود |
| COUPON_EXPIRED | الكوبون منتهي الصلاحية |
| COUPON_INACTIVE | الكوبون غير مفعل |
| USAGE_LIMIT_EXCEEDED | تم تجاوز حد الاستخدام |
| MINIMUM_AMOUNT_NOT_MET | المبلغ أقل من الحد الأدنى |
| USER_RESTRICTED | المستخدم غير مسموح له |

## 🧪 تشغيل الاختبارات

```bash
php tests/test_payment_integration.php
```

## 📁 الملفات الرئيسية

| الملف | الوصف |
|------|-------|
| includes/CouponPaymentIntegration.php | فئة التكامل الرئيسية |
| api/coupons_payment_integration.php | واجهات API |
| admin/coupon_payment_integration.php | واجهة الإدارة |
| database/migrate_payment_coupon_support.php | سكريبت الترحيل |
| tests/test_payment_integration.php | الاختبارات |

## 🔐 الأمان

- ✅ استخدام Prepared Statements
- ✅ التحقق من الصلاحيات
- ✅ تسجيل جميع العمليات
- ✅ معالجة الأخطاء
- ✅ التحقق من المدخلات

## 📞 الدعم

للمزيد من المعلومات:
- `docs/PAYMENT_INTEGRATION_GUIDE.md`
- `docs/COUPON_SYSTEM_DOCUMENTATION.md`
- `includes/CouponPaymentIntegration.php`

