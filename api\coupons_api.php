<?php
/**
 * واجهة برمجية للكوبونات - Coupons API
 * التحقق والتطبيق والإدارة
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/CouponManager.php';
require_once __DIR__ . '/../includes/CouponValidator.php';

header('Content-Type: application/json; charset=utf-8');

$pdo = getDBConnection();
$couponManager = new CouponManager($pdo);
$couponValidator = new CouponValidator($pdo, $couponManager);

$action = $_GET['action'] ?? $_POST['action'] ?? '';
$response = ['success' => false, 'message' => 'إجراء غير معروف'];

try {
    switch ($action) {
        case 'validate':
            $response = handleValidate($couponValidator);
            break;
            
        case 'apply':
            $response = handleApply($couponManager);
            break;
            
        case 'get':
            $response = handleGet($couponManager);
            break;
            
        case 'list':
            $response = handleList($pdo);
            break;
            
        case 'create':
            require_roles(['admin', 'super_admin']);
            $response = handleCreate($couponManager);
            break;
            
        case 'update':
            require_roles(['admin', 'super_admin']);
            $response = handleUpdate($couponManager);
            break;
            
        case 'delete':
            require_roles(['admin', 'super_admin']);
            $response = handleDelete($couponManager);
            break;
            
        case 'usage_report':
            require_roles(['admin', 'super_admin']);
            $response = handleUsageReport($pdo);
            break;
    }
} catch (Exception $e) {
    $response = ['success' => false, 'error' => $e->getMessage()];
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);

// ============ معالجات الإجراءات ============

function handleValidate($couponValidator) {
    $code = $_POST['code'] ?? '';
    $amount = (float)($_POST['amount'] ?? 0);
    $userId = (int)($_POST['user_id'] ?? 0);
    $planId = (int)($_POST['plan_id'] ?? 0);
    
    if (!$code) {
        return ['success' => false, 'error' => 'كود الكوبون مطلوب'];
    }
    
    $context = ['amount' => $amount];
    if ($userId) $context['user_id'] = $userId;
    if ($planId) $context['plan_id'] = $planId;
    
    return $couponValidator->validate($code, $context);
}

function handleApply($couponManager) {
    $couponId = (int)($_POST['coupon_id'] ?? 0);
    $userId = (int)($_POST['user_id'] ?? 0);
    $amount = (float)($_POST['amount'] ?? 0);
    $subscriptionId = (int)($_POST['subscription_id'] ?? 0);
    $paymentId = (int)($_POST['payment_id'] ?? 0);
    
    if (!$couponId || !$userId || !$amount) {
        return ['success' => false, 'error' => 'بيانات ناقصة'];
    }
    
    return $couponManager->applyCoupon(
        $couponId, $userId, $amount,
        $subscriptionId ?: null,
        $paymentId ?: null
    );
}

function handleGet($couponManager) {
    $couponId = (int)($_GET['id'] ?? 0);
    
    if (!$couponId) {
        return ['success' => false, 'error' => 'معرف الكوبون مطلوب'];
    }
    
    $coupon = $couponManager->getCoupon($couponId);
    
    if (!$coupon) {
        return ['success' => false, 'error' => 'الكوبون غير موجود'];
    }
    
    return ['success' => true, 'coupon' => $coupon];
}

function handleList($pdo) {
    $page = (int)($_GET['page'] ?? 1);
    $limit = (int)($_GET['limit'] ?? 20);
    $offset = ($page - 1) * $limit;
    
    $sql = "SELECT * FROM coupons ORDER BY created_at DESC LIMIT ? OFFSET ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$limit, $offset]);
    $coupons = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $countSql = "SELECT COUNT(*) as total FROM coupons";
    $total = (int)$pdo->query($countSql)->fetch(PDO::FETCH_ASSOC)['total'];
    
    return [
        'success' => true,
        'coupons' => $coupons,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'total' => $total,
            'pages' => ceil($total / $limit)
        ]
    ];
}

function handleCreate($couponManager) {
    $data = [
        'code' => $_POST['code'] ?? '',
        'name' => $_POST['name'] ?? '',
        'description' => $_POST['description'] ?? '',
        'type' => $_POST['type'] ?? 'percentage',
        'value' => (float)($_POST['value'] ?? 0),
        'minimum_amount' => (float)($_POST['minimum_amount'] ?? 0),
        'maximum_discount' => (float)($_POST['maximum_discount'] ?? 0) ?: null,
        'usage_limit' => (int)($_POST['usage_limit'] ?? 0) ?: null,
        'usage_limit_per_user' => (int)($_POST['usage_limit_per_user'] ?? 1),
        'valid_from' => $_POST['valid_from'] ?? '',
        'valid_until' => $_POST['valid_until'] ?? '',
        'is_active' => (bool)($_POST['is_active'] ?? true),
        'created_by' => $_SESSION['user_id'] ?? null
    ];
    
    if (!$data['code'] || !$data['name'] || !$data['valid_from'] || !$data['valid_until']) {
        return ['success' => false, 'error' => 'بيانات مطلوبة ناقصة'];
    }
    
    return $couponManager->createCoupon($data);
}

function handleUpdate($couponManager) {
    $couponId = (int)($_POST['id'] ?? 0);
    
    if (!$couponId) {
        return ['success' => false, 'error' => 'معرف الكوبون مطلوب'];
    }
    
    $data = [];
    $allowedFields = ['code', 'name', 'description', 'type', 'value', 'minimum_amount',
        'maximum_discount', 'usage_limit', 'usage_limit_per_user', 'valid_from', 'valid_until', 'is_active'];
    
    foreach ($allowedFields as $field) {
        if (isset($_POST[$field])) {
            $data[$field] = $_POST[$field];
        }
    }
    
    $data['updated_by'] = $_SESSION['user_id'] ?? null;
    
    return $couponManager->updateCoupon($couponId, $data);
}

function handleDelete($couponManager) {
    $couponId = (int)($_POST['id'] ?? 0);
    
    if (!$couponId) {
        return ['success' => false, 'error' => 'معرف الكوبون مطلوب'];
    }
    
    try {
        $pdo = getDBConnection();
        $pdo->prepare("DELETE FROM coupons WHERE id = ?")->execute([$couponId]);
        return ['success' => true, 'message' => 'تم حذف الكوبون'];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

function handleUsageReport($pdo) {
    $couponId = (int)($_GET['coupon_id'] ?? 0);
    $startDate = $_GET['start_date'] ?? '';
    $endDate = $_GET['end_date'] ?? '';
    
    $sql = "SELECT cu.*, c.code, c.name, u.name as user_name 
            FROM coupon_usage cu
            JOIN coupons c ON cu.coupon_id = c.id
            JOIN users u ON cu.user_id = u.id
            WHERE 1=1";
    
    $params = [];
    
    if ($couponId) {
        $sql .= " AND cu.coupon_id = ?";
        $params[] = $couponId;
    }
    
    if ($startDate) {
        $sql .= " AND cu.used_at >= ?";
        $params[] = $startDate;
    }
    
    if ($endDate) {
        $sql .= " AND cu.used_at <= ?";
        $params[] = $endDate;
    }
    
    $sql .= " ORDER BY cu.used_at DESC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $usage = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    return ['success' => true, 'usage' => $usage];
}
?>

