<?php
/**
 * اختبارات نظام الكوبونات
 * Coupon System Tests
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/CouponManager.php';
require_once __DIR__ . '/../includes/CouponValidator.php';
require_once __DIR__ . '/../includes/CouponPaymentIntegration.php';
require_once __DIR__ . '/../includes/CouponSubscriptionIntegration.php';
require_once __DIR__ . '/../includes/CouponLoyaltyIntegration.php';
require_once __DIR__ . '/../includes/CouponReportingSystem.php';

class CouponSystemTest {
    private $pdo;
    private $couponManager;
    private $couponValidator;
    private $paymentIntegration;
    private $subscriptionIntegration;
    private $loyaltyIntegration;
    private $reportingSystem;
    
    public function __construct(PDO $pdo) {
        $this->pdo = $pdo;
        $this->couponManager = new CouponManager($pdo);
        $this->couponValidator = new CouponValidator($pdo, $this->couponManager);
        $this->paymentIntegration = new CouponPaymentIntegration($pdo, $this->couponManager, $this->couponValidator);
        $this->subscriptionIntegration = new CouponSubscriptionIntegration($pdo, $this->couponManager, $this->couponValidator);
        $this->loyaltyIntegration = new CouponLoyaltyIntegration($pdo, $this->couponManager);
        $this->reportingSystem = new CouponReportingSystem($pdo);
    }
    
    /**
     * اختبار إنشاء كوبون
     */
    public function testCreateCoupon(): bool {
        echo "اختبار: إنشاء كوبون جديد\n";
        
        $result = $this->couponManager->createCoupon([
            'code' => 'TEST' . time(),
            'name' => 'كوبون اختبار',
            'type' => 'percentage',
            'value' => 20,
            'minimum_amount' => 100,
            'usage_limit' => 100,
            'usage_limit_per_user' => 5,
            'valid_from' => date('Y-m-d'),
            'valid_until' => date('Y-m-d', strtotime('+30 days')),
            'is_active' => 1
        ]);
        
        if ($result['success']) {
            echo "✓ تم إنشاء الكوبون بنجاح (ID: {$result['id']})\n";
            return true;
        } else {
            echo "✗ فشل إنشاء الكوبون: {$result['error']}\n";
            return false;
        }
    }
    
    /**
     * اختبار التحقق من الكوبون
     */
    public function testValidateCoupon(): bool {
        echo "\nاختبار: التحقق من صحة الكوبون\n";
        
        // إنشاء كوبون أولاً
        $createResult = $this->couponManager->createCoupon([
            'code' => 'VALIDATE' . time(),
            'name' => 'كوبون التحقق',
            'type' => 'fixed',
            'value' => 50,
            'minimum_amount' => 100,
            'usage_limit' => 10,
            'usage_limit_per_user' => 2,
            'valid_from' => date('Y-m-d'),
            'valid_until' => date('Y-m-d', strtotime('+30 days')),
            'is_active' => 1
        ]);
        
        if (!$createResult['success']) {
            echo "✗ فشل إنشاء الكوبون\n";
            return false;
        }
        
        // التحقق من الكوبون
        $validation = $this->couponValidator->validate($createResult['code'], [
            'amount' => 200,
            'user_id' => 1
        ]);
        
        if ($validation['valid']) {
            echo "✓ الكوبون صحيح\n";
            return true;
        } else {
            echo "✗ الكوبون غير صحيح: {$validation['error']}\n";
            return false;
        }
    }
    
    /**
     * اختبار حساب الخصم
     */
    public function testCalculateDiscount(): bool {
        echo "\nاختبار: حساب الخصم\n";
        
        $coupon = [
            'type' => 'percentage',
            'value' => 20,
            'maximum_discount' => 100
        ];
        
        $discount = $this->couponManager->calculateDiscount($coupon, 500);
        
        if ($discount == 100) {
            echo "✓ تم حساب الخصم بنجاح: {$discount}\n";
            return true;
        } else {
            echo "✗ خطأ في حساب الخصم: {$discount} (المتوقع: 100)\n";
            return false;
        }
    }
    
    /**
     * اختبار معالجة الدفع مع الكوبون
     */
    public function testPaymentWithCoupon(): bool {
        echo "\nاختبار: معالجة الدفع مع الكوبون\n";
        
        // إنشاء كوبون
        $createResult = $this->couponManager->createCoupon([
            'code' => 'PAYMENT' . time(),
            'name' => 'كوبون الدفع',
            'type' => 'percentage',
            'value' => 10,
            'minimum_amount' => 100,
            'usage_limit' => 50,
            'usage_limit_per_user' => 5,
            'valid_from' => date('Y-m-d'),
            'valid_until' => date('Y-m-d', strtotime('+30 days')),
            'is_active' => 1
        ]);
        
        if (!$createResult['success']) {
            echo "✗ فشل إنشاء الكوبون\n";
            return false;
        }
        
        // معالجة الدفع
        $result = $this->paymentIntegration->processPaymentWithCoupon([
            'coupon_code' => $createResult['code'],
            'amount' => 500,
            'user_id' => 1
        ]);
        
        if ($result['success']) {
            echo "✓ تم معالجة الدفع بنجاح\n";
            echo "  المبلغ الأصلي: {$result['original_amount']}\n";
            echo "  الخصم: {$result['discount_amount']}\n";
            echo "  المبلغ النهائي: {$result['final_amount']}\n";
            return true;
        } else {
            echo "✗ فشل معالجة الدفع: {$result['error']}\n";
            return false;
        }
    }
    
    /**
     * اختبار التقارير
     */
    public function testReporting(): bool {
        echo "\nاختبار: نظام التقارير\n";
        
        $usageReport = $this->reportingSystem->getUsageReport();
        
        if ($usageReport['success']) {
            echo "✓ تم الحصول على تقرير الاستخدام\n";
            echo "  عدد الكوبونات: " . count($usageReport['coupons']) . "\n";
            echo "  ملخص: " . json_encode($usageReport['summary']) . "\n";
            return true;
        } else {
            echo "✗ فشل الحصول على التقرير: {$usageReport['error']}\n";
            return false;
        }
    }
    
    /**
     * تشغيل جميع الاختبارات
     */
    public function runAllTests(): void {
        echo "========================================\n";
        echo "اختبارات نظام الكوبونات\n";
        echo "========================================\n";
        
        $tests = [
            'testCreateCoupon',
            'testValidateCoupon',
            'testCalculateDiscount',
            'testPaymentWithCoupon',
            'testReporting'
        ];
        
        $passed = 0;
        $failed = 0;
        
        foreach ($tests as $test) {
            if ($this->$test()) {
                $passed++;
            } else {
                $failed++;
            }
        }
        
        echo "\n========================================\n";
        echo "نتائج الاختبارات:\n";
        echo "✓ نجح: {$passed}\n";
        echo "✗ فشل: {$failed}\n";
        echo "========================================\n";
    }
}

// تشغيل الاختبارات
if (php_sapi_name() === 'cli') {
    try {
        $pdo = getDBConnection();
        $test = new CouponSystemTest($pdo);
        $test->runAllTests();
    } catch (Exception $e) {
        echo "خطأ: " . $e->getMessage() . "\n";
    }
}
?>

