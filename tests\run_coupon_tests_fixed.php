<?php
/**
 * تشغيل اختبارات نظام الكوبونات - مُصلح
 * Run Coupon System Tests - Fixed
 */

echo "🧪 تشغيل اختبارات نظام الكوبونات المُصلحة\n";
echo "=====================================\n\n";

// إعداد قاعدة البيانات
require_once __DIR__ . '/../config/database.php';

try {
    // 1. إنشاء الجداول الأساسية
    echo "📋 إنشاء الجداول الأساسية...\n";
    $sql = file_get_contents(__DIR__ . '/../database/create_coupon_tables_simple.sql');
    $pdo->exec($sql);
    echo "✅ تم إنشاء الجداول بنجاح\n\n";
    
    // 2. إضافة الأعمدة للجداول الموجودة
    echo "📋 إضافة أعمدة الكوبونات...\n";
    try {
        $sql = file_get_contents(__DIR__ . '/../database/add_coupon_columns.sql');
        $pdo->exec($sql);
        echo "✅ تم إضافة الأعمدة بنجاح\n";
    } catch (Exception $e) {
        echo "⚠️ تحذير: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 3. إدراج كوبون تجريبي
    echo "📋 إدراج كوبون تجريبي...\n";
    $sql = file_get_contents(__DIR__ . '/../database/insert_test_coupon.sql');
    $pdo->exec($sql);
    echo "✅ تم إدراج الكوبون التجريبي بنجاح\n\n";
    
    // 4. تشغيل الاختبارات
    $testFiles = [
        'test_subscription_integration.php' => 'اختبارات تكامل الاشتراكات',
        'test_payment_integration.php' => 'اختبارات تكامل الدفع',
        'test_subscription_coupon_api.php' => 'اختبارات API الاشتراكات',
        'CouponSystemTest.php' => 'اختبارات نظام الكوبونات'
    ];
    
    foreach ($testFiles as $file => $description) {
        echo "📋 تشغيل: $description\n";
        echo "الملف: $file\n";
        
        $filePath = __DIR__ . '/' . $file;
        if (file_exists($filePath)) {
            echo "✅ الملف موجود\n";
            
            try {
                // تشغيل الاختبار
                ob_start();
                include $filePath;
                $output = ob_get_clean();
                
                echo "📊 نتائج الاختبار:\n";
                echo $output . "\n";
                
            } catch (Exception $e) {
                echo "❌ خطأ في التشغيل: " . $e->getMessage() . "\n";
            }
        } else {
            echo "❌ الملف غير موجود\n";
        }
        echo str_repeat("-", 50) . "\n\n";
    }
    
    // 5. ملخص النتائج
    echo "📊 ملخص النتائج:\n";
    echo "================\n";
    
    // فحص الجداول
    $tables = ['coupons', 'coupon_usage', 'coupon_plan_mapping', 'coupon_loyalty_mapping', 'coupon_audit_log'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
            $count = $stmt->fetchColumn();
            echo "✅ جدول $table: $count سجل\n";
        } catch (Exception $e) {
            echo "❌ جدول $table: غير موجود\n";
        }
    }
    
    // فحص الكوبونات
    try {
        $stmt = $pdo->query("SELECT code, name, type, value FROM coupons WHERE is_active = 1");
        $coupons = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "\n📋 الكوبونات المفعلة:\n";
        foreach ($coupons as $coupon) {
            echo "  - {$coupon['code']}: {$coupon['name']} ({$coupon['type']}: {$coupon['value']})\n";
        }
    } catch (Exception $e) {
        echo "\n❌ خطأ في فحص الكوبونات: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
}

echo "\n✅ انتهى تشغيل الاختبارات المُصلحة\n";
?>
