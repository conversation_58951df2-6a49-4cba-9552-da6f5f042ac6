<?php
/**
 * واجهة برمجية متقدمة - تكامل الكوبونات مع الاشتراكات
 * Advanced API - Coupon Subscription Integration
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/CouponManager.php';
require_once __DIR__ . '/../includes/CouponValidator.php';
require_once __DIR__ . '/../includes/CouponSubscriptionIntegration.php';

header('Content-Type: application/json; charset=utf-8');

try {
    $pdo = getDBConnection();
    $action = $_REQUEST['action'] ?? '';
    
    // التحقق من المصادقة
    if (!isset($_SESSION['user_id'])) {
        throw new Exception('غير مصرح - يجب تسجيل الدخول');
    }
    
    $userId = (int)$_SESSION['user_id'];
    
    // إنشاء الكائنات
    $couponManager = new CouponManager($pdo);
    $couponValidator = new CouponValidator($pdo, $couponManager);
    $subscriptionIntegration = new CouponSubscriptionIntegration($pdo, $couponManager, $couponValidator);
    
    switch ($action) {
        // ============================================================================
        // 1. حساب السعر مع الكوبون
        // ============================================================================
        case 'calculate_price':
            $planId = (int)($_POST['plan_id'] ?? 0);
            $couponCode = $_POST['coupon_code'] ?? null;
            
            if (!$planId) {
                throw new Exception('معرف الخطة مطلوب');
            }
            
            $result = $subscriptionIntegration->calculateSubscriptionPrice($planId, $couponCode, $userId);
            echo json_encode($result);
            break;
        
        // ============================================================================
        // 2. إنشاء اشتراك جديد مع الكوبون
        // ============================================================================
        case 'create_with_coupon':
            $planId = (int)($_POST['plan_id'] ?? 0);
            $playerId = (int)($_POST['player_id'] ?? 0);
            $couponCode = $_POST['coupon_code'] ?? null;
            
            if (!$planId || !$playerId) {
                throw new Exception('معرف الخطة ومعرف اللاعب مطلوبان');
            }
            
            $subscriptionData = [
                'plan_id' => $planId,
                'player_id' => $playerId,
                'user_id' => $userId,
                'coupon_code' => $couponCode
            ];
            
            $result = $subscriptionIntegration->applyToNewSubscription($subscriptionData);
            echo json_encode($result);
            break;
        
        // ============================================================================
        // 3. تجديد الاشتراك مع الكوبون
        // ============================================================================
        case 'renew_with_coupon':
            $subscriptionId = (int)($_POST['subscription_id'] ?? 0);
            $couponCode = $_POST['coupon_code'] ?? null;
            
            if (!$subscriptionId) {
                throw new Exception('معرف الاشتراك مطلوب');
            }
            
            $result = $subscriptionIntegration->applyToRenewal($subscriptionId, $couponCode, $userId);
            echo json_encode($result);
            break;
        
        // ============================================================================
        // 4. ترقية الاشتراك مع الكوبون
        // ============================================================================
        case 'upgrade_with_coupon':
            $subscriptionId = (int)($_POST['subscription_id'] ?? 0);
            $newPlanId = (int)($_POST['new_plan_id'] ?? 0);
            $couponCode = $_POST['coupon_code'] ?? null;
            
            if (!$subscriptionId || !$newPlanId) {
                throw new Exception('معرف الاشتراك والخطة الجديدة مطلوبان');
            }
            
            $result = $subscriptionIntegration->applyToUpgrade($subscriptionId, $newPlanId, $couponCode, $userId);
            echo json_encode($result);
            break;
        
        // ============================================================================
        // 5. إزالة الكوبون من الاشتراك
        // ============================================================================
        case 'remove_coupon':
            $subscriptionId = (int)($_POST['subscription_id'] ?? 0);
            
            if (!$subscriptionId) {
                throw new Exception('معرف الاشتراك مطلوب');
            }
            
            $result = $subscriptionIntegration->removeCoupon($subscriptionId, $userId);
            echo json_encode($result);
            break;
        
        // ============================================================================
        // 6. التحقق من صحة الكوبون
        // ============================================================================
        case 'validate_coupon':
            $couponCode = $_POST['coupon_code'] ?? null;
            $planId = (int)($_POST['plan_id'] ?? 0);
            
            if (!$couponCode || !$planId) {
                throw new Exception('كود الكوبون ومعرف الخطة مطلوبان');
            }
            
            $context = [
                'amount' => 0,
                'plan_id' => $planId,
                'user_id' => $userId
            ];
            
            $validation = $couponValidator->validate($couponCode, $context);
            echo json_encode($validation);
            break;
        
        // ============================================================================
        // 7. الحصول على بيانات الاشتراك
        // ============================================================================
        case 'get_subscription':
            $subscriptionId = (int)($_POST['subscription_id'] ?? 0);
            
            if (!$subscriptionId) {
                throw new Exception('معرف الاشتراك مطلوب');
            }
            
            $subscription = $subscriptionIntegration->getSubscription($subscriptionId);
            echo json_encode([
                'success' => $subscription !== null,
                'subscription' => $subscription
            ]);
            break;
        
        // ============================================================================
        // 8. الحصول على قائمة الاشتراكات
        // ============================================================================
        case 'list_subscriptions':
            $playerId = (int)($_POST['player_id'] ?? 0);
            
            if (!$playerId) {
                throw new Exception('معرف اللاعب مطلوب');
            }
            
            $stmt = $pdo->prepare("SELECT * FROM player_subscriptions WHERE player_id = ? ORDER BY created_at DESC");
            $stmt->execute([$playerId]);
            $subscriptions = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'subscriptions' => $subscriptions,
                'count' => count($subscriptions)
            ]);
            break;
        
        default:
            throw new Exception('إجراء غير معروف: ' . $action);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>

