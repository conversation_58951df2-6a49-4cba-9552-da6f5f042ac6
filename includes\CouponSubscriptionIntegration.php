<?php
/**
 * تكامل الكوبونات مع نظام الاشتراكات
 * Coupon Subscription Integration
 */

class CouponSubscriptionIntegration {
    private $pdo;
    private $couponManager;
    private $couponValidator;
    
    public function __construct(PDO $pdo, CouponManager $couponManager, CouponValidator $couponValidator) {
        $this->pdo = $pdo;
        $this->couponManager = $couponManager;
        $this->couponValidator = $couponValidator;
    }
    
    /**
     * حساب سعر الاشتراك مع الكوبون
     */
    public function calculateSubscriptionPrice(int $planId, ?string $couponCode = null, int $userId = 0): array {
        try {
            // الحصول على سعر الخطة
            $plan = $this->getPlan($planId);
            if (!$plan) {
                return ['success' => false, 'error' => 'الخطة غير موجودة'];
            }
            
            $originalPrice = (float)$plan['price'];
            $discountAmount = 0;
            $finalPrice = $originalPrice;
            $couponId = null;
            
            // التحقق من الكوبون
            if ($couponCode) {
                $context = [
                    'amount' => $originalPrice,
                    'plan_id' => $planId
                ];
                
                if ($userId) {
                    $context['user_id'] = $userId;
                }
                
                $validation = $this->couponValidator->validate($couponCode, $context);
                
                if (!$validation['valid']) {
                    return [
                        'success' => false,
                        'error' => $validation['error'],
                        'original_price' => $originalPrice,
                        'final_price' => $originalPrice
                    ];
                }
                
                $coupon = $validation['coupon'];
                $couponId = $coupon['id'];
                
                // حساب الخصم
                $discountAmount = $this->couponManager->calculateDiscount($coupon, $originalPrice);
                $finalPrice = $originalPrice - $discountAmount;
            }
            
            return [
                'success' => true,
                'plan_id' => $planId,
                'plan_name' => $plan['name'],
                'original_price' => $originalPrice,
                'discount_amount' => $discountAmount,
                'final_price' => $finalPrice,
                'coupon_id' => $couponId,
                'coupon_code' => $couponCode
            ];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * تطبيق الكوبون على الاشتراك الجديد
     */
    public function applyToNewSubscription(array $subscriptionData): array {
        try {
            $couponCode = $subscriptionData['coupon_code'] ?? null;
            $planId = (int)$subscriptionData['plan_id'];
            $userId = (int)$subscriptionData['user_id'];
            $playerId = (int)($subscriptionData['player_id'] ?? 0);
            
            // حساب السعر
            $priceCalc = $this->calculateSubscriptionPrice($planId, $couponCode, $userId);
            
            if (!$priceCalc['success']) {
                return $priceCalc;
            }
            
            // إنشاء الاشتراك
            $sql = "INSERT INTO subscriptions (
                player_id, plan_id, coupon_id,
                total_amount, start_date, end_date, status
            ) VALUES (?, ?, ?, ?, ?, ?, 'active')";

            $startDate = new DateTime();
            $endDate = clone $startDate;
            $endDate->add(new DateInterval('P1M')); // إضافة شهر واحد

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([
                $playerId,
                $planId,
                $priceCalc['coupon_id'],
                $priceCalc['final_price'],
                $startDate->format('Y-m-d'),
                $endDate->format('Y-m-d')
            ]);
            
            $subscriptionId = $this->pdo->lastInsertId();
            
            // تطبيق الكوبون إذا وجد
            if ($priceCalc['coupon_id']) {
                $this->couponManager->applyCoupon(
                    $priceCalc['coupon_id'],
                    $userId,
                    $priceCalc['original_price'],
                    $subscriptionId,
                    null
                );
            }
            
            return [
                'success' => true,
                'subscription_id' => $subscriptionId,
                'original_amount' => $priceCalc['original_price'],
                'discount_amount' => $priceCalc['discount_amount'],
                'final_amount' => $priceCalc['final_price'],
                'message' => 'تم إنشاء الاشتراك بنجاح'
            ];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * تطبيق الكوبون على تجديد الاشتراك
     */
    public function applyToRenewal(int $subscriptionId, ?string $couponCode = null): array {
        try {
            $subscription = $this->getSubscription($subscriptionId);
            if (!$subscription) {
                return ['success' => false, 'error' => 'الاشتراك غير موجود'];
            }
            
            $plan = $this->getPlan($subscription['plan_id']);
            if (!$plan) {
                return ['success' => false, 'error' => 'الخطة غير موجودة'];
            }
            
            $originalPrice = (float)$plan['price'];
            $discountAmount = 0;
            $finalPrice = $originalPrice;
            $couponId = null;
            
            // التحقق من الكوبون
            if ($couponCode) {
                $context = [
                    'amount' => $originalPrice,
                    'plan_id' => $subscription['plan_id'],
                    'user_id' => $subscription['user_id']
                ];
                
                $validation = $this->couponValidator->validate($couponCode, $context);
                
                if (!$validation['valid']) {
                    return [
                        'success' => false,
                        'error' => $validation['error']
                    ];
                }
                
                $coupon = $validation['coupon'];
                $couponId = $coupon['id'];
                $discountAmount = $this->couponManager->calculateDiscount($coupon, $originalPrice);
                $finalPrice = $originalPrice - $discountAmount;
            }
            
            // تحديث الاشتراك
            $endDate = new DateTime($subscription['end_date']);
            $endDate->add(new DateInterval('P1M'));
            
            $sql = "UPDATE subscriptions SET 
                    coupon_id = ?,
                    original_amount = ?,
                    discount_amount = ?,
                    final_amount = ?,
                    end_date = ?,
                    updated_at = NOW()
                    WHERE id = ?";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([
                $couponId,
                $originalPrice,
                $discountAmount,
                $finalPrice,
                $endDate->format('Y-m-d'),
                $subscriptionId
            ]);
            
            // تطبيق الكوبون إذا وجد
            if ($couponId) {
                $this->couponManager->applyCoupon(
                    $couponId,
                    $subscription['user_id'],
                    $originalPrice,
                    $subscriptionId,
                    null
                );
            }
            
            return [
                'success' => true,
                'original_amount' => $originalPrice,
                'discount_amount' => $discountAmount,
                'final_amount' => $finalPrice,
                'new_end_date' => $endDate->format('Y-m-d'),
                'message' => 'تم تجديد الاشتراك بنجاح'
            ];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * تطبيق الكوبون على ترقية الاشتراك
     */
    public function applyToUpgrade(int $subscriptionId, int $newPlanId, ?string $couponCode = null): array {
        try {
            $subscription = $this->getSubscription($subscriptionId);
            if (!$subscription) {
                return ['success' => false, 'error' => 'الاشتراك غير موجود'];
            }

            $newPlan = $this->getPlan($newPlanId);
            if (!$newPlan) {
                return ['success' => false, 'error' => 'الخطة الجديدة غير موجودة'];
            }

            $originalPrice = (float)$newPlan['price'];
            $discountAmount = 0;
            $finalPrice = $originalPrice;
            $couponId = null;

            if ($couponCode) {
                $context = [
                    'amount' => $originalPrice,
                    'plan_id' => $newPlanId,
                    'user_id' => $subscription['user_id']
                ];

                $validation = $this->couponValidator->validate($couponCode, $context);

                if (!$validation['valid']) {
                    return ['success' => false, 'error' => $validation['error']];
                }

                $coupon = $validation['coupon'];
                $couponId = $coupon['id'];
                $discountAmount = $this->couponManager->calculateDiscount($coupon, $originalPrice);
                $finalPrice = $originalPrice - $discountAmount;
            }

            $sql = "UPDATE player_subscriptions SET
                    plan_id = ?,
                    coupon_id = ?,
                    original_amount = ?,
                    coupon_discount_amount = ?,
                    final_amount = ?,
                    updated_at = NOW()
                    WHERE id = ?";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([
                $newPlanId,
                $couponId,
                $originalPrice,
                $discountAmount,
                $finalPrice,
                $subscriptionId
            ]);

            if ($couponId) {
                $this->couponManager->applyCoupon(
                    $couponId,
                    $subscription['player_id'],
                    $originalPrice,
                    $subscriptionId,
                    null
                );
            }

            return [
                'success' => true,
                'original_amount' => $originalPrice,
                'discount_amount' => $discountAmount,
                'final_amount' => $finalPrice,
                'message' => 'تم ترقية الاشتراك بنجاح'
            ];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * إزالة الكوبون من الاشتراك
     */
    public function removeCoupon(int $subscriptionId): array {
        try {
            $subscription = $this->getSubscription($subscriptionId);
            if (!$subscription) {
                return ['success' => false, 'error' => 'الاشتراك غير موجود'];
            }

            $sql = "UPDATE player_subscriptions SET
                    coupon_id = NULL,
                    coupon_code = NULL,
                    coupon_discount_amount = 0,
                    final_amount = original_amount,
                    updated_at = NOW()
                    WHERE id = ?";

            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$subscriptionId]);

            return [
                'success' => true,
                'message' => 'تم إزالة الكوبون بنجاح'
            ];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * الحصول على الخطة
     */
    private function getPlan(int $planId): ?array {
        $sql = "SELECT * FROM subscription_plans WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$planId]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }

    /**
     * الحصول على الاشتراك
     */
    public function getSubscription(int $subscriptionId): ?array {
        $sql = "SELECT * FROM player_subscriptions WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$subscriptionId]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }
}
?>

