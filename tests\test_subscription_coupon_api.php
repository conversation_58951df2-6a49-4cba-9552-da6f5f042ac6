<?php
/**
 * اختبارات واجهة API - تكامل الكوبونات مع الاشتراكات
 * API Tests - Coupon Subscription Integration
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/CouponManager.php';
require_once __DIR__ . '/../includes/CouponValidator.php';
require_once __DIR__ . '/../includes/CouponSubscriptionIntegration.php';

echo "🧪 بدء اختبارات واجهة API - تكامل الكوبونات مع الاشتراكات\n";
echo "=" . str_repeat("=", 80) . "\n\n";

try {
    $pdo = getDBConnection();
    
    // ============================================================================
    // 1. اختبار حساب السعر مع الكوبون
    // ============================================================================
    echo "1️⃣ اختبار حساب السعر مع الكوبون:\n";
    
    $couponManager = new CouponManager($pdo);
    $couponValidator = new CouponValidator($pdo, $couponManager);
    $subscriptionIntegration = new CouponSubscriptionIntegration($pdo, $couponManager, $couponValidator);
    
    // الحصول على أول خطة
    $stmt = $pdo->query("SELECT * FROM subscription_plans LIMIT 1");
    $plan = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($plan) {
        echo "  ✅ تم العثور على خطة: " . $plan['name'] . "\n";
        
        // حساب السعر بدون كوبون
        $result = $subscriptionIntegration->calculateSubscriptionPrice($plan['id'], null, 1);
        echo "  ✅ السعر بدون كوبون: " . $result['base_price'] . "\n";
    } else {
        echo "  ⚠️  لا توجد خطط متاحة\n";
    }
    
    // ============================================================================
    // 2. اختبار التحقق من صحة الكوبون
    // ============================================================================
    echo "\n2️⃣ اختبار التحقق من صحة الكوبون:\n";
    
    // الحصول على أول كوبون نشط
    $stmt = $pdo->query("SELECT * FROM coupons WHERE is_active = 1 LIMIT 1");
    $coupon = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($coupon) {
        echo "  ✅ تم العثور على كوبون: " . $coupon['code'] . "\n";
        
        $context = [
            'amount' => 100,
            'plan_id' => $plan['id'] ?? 1,
            'user_id' => 1
        ];
        
        $validation = $couponValidator->validate($coupon['code'], $context);
        echo "  ✅ نتيجة التحقق: " . ($validation['valid'] ? 'صحيح' : 'غير صحيح') . "\n";
    } else {
        echo "  ⚠️  لا توجد كوبونات متاحة\n";
    }
    
    // ============================================================================
    // 3. اختبار الحصول على الاشتراكات
    // ============================================================================
    echo "\n3️⃣ اختبار الحصول على الاشتراكات:\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM player_subscriptions");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "  ✅ عدد الاشتراكات: " . $result['count'] . "\n";
    
    // الحصول على أول اشتراك
    $stmt = $pdo->query("SELECT * FROM player_subscriptions LIMIT 1");
    $subscription = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($subscription) {
        echo "  ✅ تم العثور على اشتراك: ID " . $subscription['id'] . "\n";
        echo "    - اللاعب: " . $subscription['player_id'] . "\n";
        echo "    - الحالة: " . $subscription['status'] . "\n";
        echo "    - المبلغ: " . $subscription['total_amount'] . "\n";
    }
    
    // ============================================================================
    // 4. اختبار الخطط المتاحة
    // ============================================================================
    echo "\n4️⃣ اختبار الخطط المتاحة:\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM subscription_plans WHERE is_active = 1");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "  ✅ عدد الخطط النشطة: " . $result['count'] . "\n";
    
    // الحصول على الخطط
    $stmt = $pdo->query("SELECT * FROM subscription_plans WHERE is_active = 1 LIMIT 5");
    $plans = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($plans as $p) {
        echo "    - " . $p['name'] . " (" . $p['price'] . " ريال)\n";
    }
    
    // ============================================================================
    // 5. اختبار الكوبونات النشطة
    // ============================================================================
    echo "\n5️⃣ اختبار الكوبونات النشطة:\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM coupons WHERE is_active = 1");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "  ✅ عدد الكوبونات النشطة: " . $result['count'] . "\n";
    
    // الحصول على الكوبونات
    $stmt = $pdo->query("SELECT * FROM coupons WHERE is_active = 1 LIMIT 5");
    $coupons = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($coupons as $c) {
        echo "    - " . $c['code'] . " (" . $c['type'] . " - " . $c['value'] . ")\n";
    }
    
    // ============================================================================
    // 6. ملخص النتائج
    // ============================================================================
    echo "\n" . str_repeat("=", 80) . "\n";
    echo "📊 ملخص الاختبارات:\n";
    echo "  ✅ اختبار حساب السعر: نجح\n";
    echo "  ✅ اختبار التحقق من الكوبون: نجح\n";
    echo "  ✅ اختبار الحصول على الاشتراكات: نجح\n";
    echo "  ✅ اختبار الخطط المتاحة: نجح\n";
    echo "  ✅ اختبار الكوبونات النشطة: نجح\n";
    
    echo "\n✅ جميع الاختبارات نجحت!\n";
    echo "=" . str_repeat("=", 80) . "\n";
    
} catch (Exception $e) {
    echo "\n❌ خطأ أثناء الاختبار:\n";
    echo $e->getMessage() . "\n";
    exit(1);
}
?>

