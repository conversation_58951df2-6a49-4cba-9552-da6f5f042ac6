-- =====================================================
-- إعداد نهائي لنظام الكوبونات - مُحدث
-- Final Coupon System Setup - Updated
-- =====================================================

-- حذ<PERSON> الجداول إذا كانت موجودة
DROP TABLE IF EXISTS `coupon_usage`;
DROP TABLE IF EXISTS `coupons`;

-- إن<PERSON>اء جدول الكوبونات الكامل
CREATE TABLE `coupons` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `uuid` CHAR(36) UNIQUE NOT NULL,
  `code` VARCHAR(50) UNIQUE NOT NULL,
  `name` VARCHAR(100) NOT NULL,
  `description` TEXT NULL,
  `type` ENUM('percentage','fixed','free_month','free_session','upgrade') NOT NULL DEFAULT 'percentage',
  `value` DECIMAL(10,2) NOT NULL,
  `minimum_amount` DECIMAL(10,2) DEFAULT 0,
  `maximum_discount` DECIMAL(10,2) NULL,
  `usage_limit` INT UNSIGNED NULL,
  `usage_limit_per_user` TINYINT UNSIGNED DEFAULT 1,
  `used_count` INT UNSIGNED DEFAULT 0,
  `valid_from` DATETIME NOT NULL,
  `valid_until` DATETIME NOT NULL,
  `is_active` BOOLEAN DEFAULT TRUE,
  `applicable_to` JSON NULL,
  `user_restrictions` JSON NULL,
  `excluded_users` JSON NULL,
  `created_by` BIGINT UNSIGNED NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_code` (`code`),
  INDEX `idx_is_active` (`is_active`),
  INDEX `idx_valid_dates` (`valid_from`, `valid_until`),
  INDEX `idx_used_count` (`used_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول استخدام الكوبونات
CREATE TABLE `coupon_usage` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `coupon_id` BIGINT UNSIGNED NOT NULL,
  `user_id` BIGINT UNSIGNED NOT NULL,
  `player_id` BIGINT UNSIGNED NULL,
  `subscription_id` BIGINT UNSIGNED NULL,
  `payment_id` BIGINT UNSIGNED NULL,
  `invoice_id` BIGINT UNSIGNED NULL,
  `discount_amount` DECIMAL(10,2) NOT NULL,
  `original_amount` DECIMAL(10,2) NOT NULL,
  `final_amount` DECIMAL(10,2) NOT NULL,
  `status` ENUM('applied','cancelled','refunded') DEFAULT 'applied',
  `used_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `cancelled_at` TIMESTAMP NULL,
  `notes` TEXT NULL,
  INDEX `idx_coupon_id` (`coupon_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_player_id` (`player_id`),
  INDEX `idx_subscription_id` (`subscription_id`),
  INDEX `idx_payment_id` (`payment_id`),
  INDEX `idx_used_at` (`used_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج كوبونات تجريبية
INSERT INTO `coupons` (
    `uuid`,
    `code`,
    `name`,
    `description`,
    `type`,
    `value`,
    `minimum_amount`,
    `maximum_discount`,
    `usage_limit`,
    `usage_limit_per_user`,
    `used_count`,
    `valid_from`,
    `valid_until`,
    `is_active`,
    `applicable_to`,
    `user_restrictions`,
    `excluded_users`,
    `created_by`
) VALUES
(UUID(), 'TEST2025', 'كوبون اختبار 2025', 'كوبون تجريبي للاختبار', 'percentage', 10.00, 50.00, 100.00, 100, 1, 0, '2025-01-01 00:00:00', '2025-12-31 23:59:59', 1, JSON_ARRAY('all'), JSON_ARRAY(), JSON_ARRAY(), 1),
(UUID(), 'FIXED50', 'خصم ثابت 50', 'كوبون خصم ثابت 50 ريال', 'fixed', 50.00, 100.00, NULL, 50, 1, 0, '2025-01-01 00:00:00', '2025-12-31 23:59:59', 1, JSON_ARRAY('all'), JSON_ARRAY(), JSON_ARRAY(), 1),
(UUID(), 'SIMPLE2025', 'كوبون بسيط 2025', 'كوبون اختبار بسيط', 'percentage', 15.00, 30.00, 200.00, 200, 1, 0, '2025-01-01 00:00:00', '2025-12-31 23:59:59', 1, JSON_ARRAY('all'), JSON_ARRAY(), JSON_ARRAY(), 1);

-- فحص النتائج
SELECT 'تم إنشاء الجداول وإدراج الكوبونات بنجاح!' AS message;
SELECT COUNT(*) AS total_coupons FROM coupons;
SELECT id, code, name, type, value, is_active FROM coupons WHERE is_active = 1;
