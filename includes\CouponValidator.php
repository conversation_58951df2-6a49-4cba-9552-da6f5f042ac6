<?php
/**
 * مدقق الكوبونات - Coupon Validator
 * التحقق المتقدم من صحة الكوبونات مع قيود معقدة
 */

class CouponValidator {
    private $pdo;
    private $couponManager;
    
    public function __construct(PDO $pdo, CouponManager $couponManager) {
        $this->pdo = $pdo;
        $this->couponManager = $couponManager;
    }
    
    /**
     * التحقق الشامل من الكوبون
     */
    public function validate(string $code, array $context): array {
        $coupon = $this->couponManager->getCouponByCode($code);
        
        if (!$coupon) {
            return $this->error('الكوبون غير موجود');
        }
        
        // التحقق من الحالة
        if (!$coupon['is_active']) {
            return $this->error('الكوبون معطل');
        }
        
        // التحقق من الصلاحية
        $dateCheck = $this->validateDates($coupon);
        if (!$dateCheck['valid']) {
            return $this->error($dateCheck['error']);
        }
        
        // التحقق من حد الاستخدام
        $usageCheck = $this->validateUsageLimit($coupon);
        if (!$usageCheck['valid']) {
            return $this->error($usageCheck['error']);
        }
        
        // التحقق من حد الاستخدام للمستخدم
        if (isset($context['user_id'])) {
            $userUsageCheck = $this->validateUserUsageLimit($coupon, $context['user_id']);
            if (!$userUsageCheck['valid']) {
                return $this->error($userUsageCheck['error']);
            }
        }
        
        // التحقق من المبلغ الأدنى
        if (isset($context['amount'])) {
            $amountCheck = $this->validateMinimumAmount($coupon, $context['amount']);
            if (!$amountCheck['valid']) {
                return $this->error($amountCheck['error']);
            }
        }
        
        // التحقق من قيود المستخدم
        if (isset($context['user_id'])) {
            $restrictionCheck = $this->validateUserRestrictions($coupon, $context);
            if (!$restrictionCheck['valid']) {
                return $this->error($restrictionCheck['error']);
            }
        }
        
        // التحقق من الخطط المسموحة
        if (isset($context['plan_id'])) {
            $planCheck = $this->validateApplicablePlans($coupon, $context['plan_id']);
            if (!$planCheck['valid']) {
                return $this->error($planCheck['error']);
            }
        }
        
        return [
            'valid' => true,
            'coupon' => $coupon,
            'message' => 'الكوبون صحيح'
        ];
    }
    
    /**
     * التحقق من صلاحية التاريخ
     */
    private function validateDates(array $coupon): array {
        $now = new DateTime();
        $validFrom = new DateTime($coupon['valid_from']);
        $validUntil = new DateTime($coupon['valid_until']);
        
        if ($now < $validFrom) {
            return ['valid' => false, 'error' => 'الكوبون لم يبدأ بعد'];
        }
        
        if ($now > $validUntil) {
            return ['valid' => false, 'error' => 'الكوبون منتهي الصلاحية'];
        }
        
        return ['valid' => true];
    }
    
    /**
     * التحقق من حد الاستخدام الكلي
     */
    private function validateUsageLimit(array $coupon): array {
        if ($coupon['usage_limit'] && $coupon['used_count'] >= $coupon['usage_limit']) {
            return ['valid' => false, 'error' => 'تم استنفاد حد الاستخدام'];
        }
        
        return ['valid' => true];
    }
    
    /**
     * التحقق من حد الاستخدام للمستخدم
     */
    private function validateUserUsageLimit(array $coupon, int $userId): array {
        $sql = "SELECT COUNT(*) as count FROM coupon_usage 
                WHERE coupon_id = ? AND user_id = ? AND status = 'applied'";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$coupon['id'], $userId]);
        $count = (int)$stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($count >= $coupon['usage_limit_per_user']) {
            return ['valid' => false, 'error' => 'لقد استخدمت هذا الكوبون بالفعل'];
        }
        
        return ['valid' => true];
    }
    
    /**
     * التحقق من المبلغ الأدنى
     */
    private function validateMinimumAmount(array $coupon, float $amount): array {
        if ($amount < $coupon['minimum_amount']) {
            return [
                'valid' => false,
                'error' => "المبلغ أقل من الحد الأدنى ({$coupon['minimum_amount']} ريال)"
            ];
        }
        
        return ['valid' => true];
    }
    
    /**
     * التحقق من قيود المستخدم
     */
    private function validateUserRestrictions(array $coupon, array $context): array {
        if (!$coupon['user_restrictions']) {
            return ['valid' => true];
        }
        
        $restrictions = json_decode($coupon['user_restrictions'], true) ?? [];
        
        // التحقق من الأعضاء الجدد
        if (in_array('new_members', $restrictions)) {
            $isNewMember = $this->isNewMember($context['user_id']);
            if (!$isNewMember) {
                return ['valid' => false, 'error' => 'هذا الكوبون للأعضاء الجدد فقط'];
            }
        }
        
        // التحقق من الأعضاء VIP
        if (in_array('vip', $restrictions)) {
            $isVIP = $this->isVIPMember($context['user_id']);
            if (!$isVIP) {
                return ['valid' => false, 'error' => 'هذا الكوبون للأعضاء VIP فقط'];
            }
        }
        
        return ['valid' => true];
    }
    
    /**
     * التحقق من الخطط المسموحة
     */
    private function validateApplicablePlans(array $coupon, int $planId): array {
        if (!$coupon['applicable_to']) {
            return ['valid' => true];
        }
        
        $applicablePlans = json_decode($coupon['applicable_to'], true) ?? [];
        
        if (!in_array($planId, $applicablePlans)) {
            return ['valid' => false, 'error' => 'هذا الكوبون غير قابل للتطبيق على هذه الخطة'];
        }
        
        return ['valid' => true];
    }
    
    /**
     * التحقق من كون المستخدم عضو جديد
     */
    private function isNewMember(int $userId): bool {
        $sql = "SELECT created_at FROM users WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) return false;
        
        $createdAt = new DateTime($user['created_at']);
        $now = new DateTime();
        $interval = $now->diff($createdAt);
        
        return $interval->days <= 30; // عضو جديد إذا كان أقل من 30 يوم
    }
    
    /**
     * التحقق من كون المستخدم VIP
     */
    private function isVIPMember(int $userId): bool {
        $sql = "SELECT role FROM users WHERE id = ? AND role IN ('vip', 'premium')";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$userId]);
        return (bool)$stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * إرجاع خطأ
     */
    private function error(string $message): array {
        return ['valid' => false, 'error' => $message];
    }
}
?>

