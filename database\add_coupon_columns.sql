-- =====================================================
-- إضافة أعمدة الكوبونات للجداول الموجودة
-- Add Coupon Columns to Existing Tables
-- =====================================================

-- تحديث جدول الفواتير (إضافة أعمدة الكوبونات)
ALTER TABLE `invoices` 
ADD COLUMN IF NOT EXISTS `coupon_id` BIGINT UNSIGNED NULL COMMENT 'معرف الكوبون المطبق' AFTER `discount`,
ADD COLUMN IF NOT EXISTS `coupon_code` VARCHAR(50) NULL COMMENT 'كود الكوبون' AFTER `coupon_id`,
ADD COLUMN IF NOT EXISTS `coupon_discount_amount` DECIMAL(10,2) DEFAULT 0 COMMENT 'مبلغ خصم الكوبون' AFTER `coupon_code`,
ADD COLUMN IF NOT EXISTS `final_amount` DECIMAL(10,2) NULL COMMENT 'المبلغ النهائي بعد الخصم' AFTER `coupon_discount_amount`;

-- إضافة فهارس للفواتير
ALTER TABLE `invoices` 
ADD INDEX IF NOT EXISTS `idx_coupon_id` (`coupon_id`),
ADD INDEX IF NOT EXISTS `idx_coupon_code` (`coupon_code`);

-- تحديث جدول المدفوعات (إضافة أعمدة الكوبونات)
ALTER TABLE `payments` 
ADD COLUMN IF NOT EXISTS `coupon_id` BIGINT UNSIGNED NULL COMMENT 'معرف الكوبون المطبق' AFTER `amount`,
ADD COLUMN IF NOT EXISTS `coupon_code` VARCHAR(50) NULL COMMENT 'كود الكوبون' AFTER `coupon_id`,
ADD COLUMN IF NOT EXISTS `discount_amount` DECIMAL(10,2) DEFAULT 0 COMMENT 'مبلغ الخصم' AFTER `coupon_code`,
ADD COLUMN IF NOT EXISTS `final_amount` DECIMAL(10,2) NULL COMMENT 'المبلغ النهائي بعد الخصم' AFTER `discount_amount`,
ADD COLUMN IF NOT EXISTS `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'آخر تحديث' AFTER `final_amount`;

-- إضافة فهارس للمدفوعات
ALTER TABLE `payments` 
ADD INDEX IF NOT EXISTS `idx_coupon_id` (`coupon_id`),
ADD INDEX IF NOT EXISTS `idx_coupon_code` (`coupon_code`);

-- تحديث جدول الاشتراكات (إضافة أعمدة الكوبونات)
ALTER TABLE `subscriptions` 
ADD COLUMN IF NOT EXISTS `coupon_id` BIGINT UNSIGNED NULL COMMENT 'معرف الكوبون المطبق' AFTER `price`,
ADD COLUMN IF NOT EXISTS `coupon_code` VARCHAR(50) NULL COMMENT 'كود الكوبون' AFTER `coupon_id`,
ADD COLUMN IF NOT EXISTS `discount_amount` DECIMAL(10,2) DEFAULT 0 COMMENT 'مبلغ الخصم' AFTER `coupon_code`,
ADD COLUMN IF NOT EXISTS `final_price` DECIMAL(10,2) NULL COMMENT 'السعر النهائي بعد الخصم' AFTER `discount_amount`;

-- إضافة فهارس للاشتراكات
ALTER TABLE `subscriptions` 
ADD INDEX IF NOT EXISTS `idx_coupon_id` (`coupon_id`),
ADD INDEX IF NOT EXISTS `idx_coupon_code` (`coupon_code`);

-- تحديث جدول خطط الاشتراك (إضافة دعم الكوبونات)
ALTER TABLE `subscription_plans` 
ADD COLUMN IF NOT EXISTS `allows_coupons` BOOLEAN DEFAULT TRUE COMMENT 'هل تسمح الخطة بالكوبونات',
ADD COLUMN IF NOT EXISTS `max_coupon_discount` DECIMAL(5,2) DEFAULT 100.00 COMMENT 'أقصى نسبة خصم مسموحة';

-- إضافة فهرس لخطط الاشتراك
ALTER TABLE `subscription_plans` 
ADD INDEX IF NOT EXISTS `idx_allows_coupons` (`allows_coupons`);

-- رسالة نجاح
SELECT 'تم إضافة أعمدة الكوبونات بنجاح!' AS message;
