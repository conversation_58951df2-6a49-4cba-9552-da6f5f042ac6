<?php
/**
 * سكريبت التحقق من قاعدة البيانات - نظام الاشتراكات مع الكوبونات
 * Database Verification Script - Subscription System with Coupons
 */

require_once __DIR__ . '/../config.php';

echo "🔍 بدء التحقق من قاعدة البيانات - نظام الاشتراكات مع الكوبونات\n";
echo "=" . str_repeat("=", 80) . "\n\n";

try {
    $pdo = getDBConnection();
    
    // ============================================================================
    // 1. التحقق من الجداول الأساسية
    // ============================================================================
    echo "1️⃣ التحقق من الجداول الأساسية:\n";
    
    $requiredTables = [
        'player_subscriptions' => 'جدول الاشتراكات',
        'subscription_plans' => 'جدول خطط الاشتراك',
        'coupons' => 'جدول الكوبونات',
        'coupon_usage' => 'جدول استخدام الكوبونات'
    ];
    
    foreach ($requiredTables as $table => $desc) {
        $result = $pdo->query("SHOW TABLES LIKE '$table'")->fetchAll();
        if (!empty($result)) {
            echo "  ✅ جدول '$table' ($desc) موجود\n";
        } else {
            echo "  ❌ جدول '$table' ($desc) غير موجود\n";
        }
    }
    
    // ============================================================================
    // 2. التحقق من أعمدة player_subscriptions
    // ============================================================================
    echo "\n2️⃣ التحقق من أعمدة جدول player_subscriptions:\n";
    
    $stmt = $pdo->query("DESCRIBE player_subscriptions");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $columnNames = array_column($columns, 'Field');
    
    $requiredColumns = [
        'id' => 'معرف الاشتراك',
        'player_id' => 'معرف اللاعب',
        'plan_id' => 'معرف الخطة',
        'start_date' => 'تاريخ البداية',
        'end_date' => 'تاريخ النهاية',
        'status' => 'حالة الاشتراك',
        'payment_status' => 'حالة الدفع',
        'total_amount' => 'المبلغ الكلي',
        'coupon_id' => 'معرف الكوبون',
        'coupon_code' => 'كود الكوبون',
        'original_amount' => 'المبلغ الأصلي',
        'coupon_discount_amount' => 'مبلغ الخصم',
        'final_amount' => 'المبلغ النهائي',
        'coupon_applied_at' => 'وقت التطبيق',
        'coupon_applied_by' => 'من طبق الكوبون'
    ];
    
    $missingColumns = [];
    foreach ($requiredColumns as $col => $desc) {
        if (in_array($col, $columnNames)) {
            echo "  ✅ العمود '$col' ($desc) موجود\n";
        } else {
            echo "  ❌ العمود '$col' ($desc) غير موجود\n";
            $missingColumns[] = $col;
        }
    }
    
    // ============================================================================
    // 3. التحقق من الفهارس
    // ============================================================================
    echo "\n3️⃣ التحقق من الفهارس:\n";
    
    $stmt = $pdo->query("SHOW INDEXES FROM player_subscriptions");
    $indexes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $indexNames = array_column($indexes, 'Key_name');
    
    $requiredIndexes = [
        'idx_coupon_id' => 'فهرس معرف الكوبون',
        'idx_coupon_code' => 'فهرس كود الكوبون',
        'idx_coupon_applied' => 'فهرس وقت التطبيق'
    ];
    
    foreach ($requiredIndexes as $idx => $desc) {
        if (in_array($idx, $indexNames)) {
            echo "  ✅ الفهرس '$idx' ($desc) موجود\n";
        } else {
            echo "  ⚠️  الفهرس '$idx' ($desc) غير موجود\n";
        }
    }
    
    // ============================================================================
    // 4. التحقق من العلاقات (Foreign Keys)
    // ============================================================================
    echo "\n4️⃣ التحقق من العلاقات (Foreign Keys):\n";
    
    $stmt = $pdo->query("SELECT CONSTRAINT_NAME FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                        WHERE TABLE_NAME = 'player_subscriptions' AND COLUMN_NAME = 'coupon_id'");
    $fks = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($fks)) {
        echo "  ✅ علاقة Foreign Key للكوبونات موجودة\n";
    } else {
        echo "  ⚠️  علاقة Foreign Key للكوبونات غير موجودة\n";
    }
    
    // ============================================================================
    // 5. إحصائيات الجداول
    // ============================================================================
    echo "\n5️⃣ إحصائيات الجداول:\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM player_subscriptions");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "  📊 عدد الاشتراكات: " . $result['count'] . "\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM subscription_plans");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "  📊 عدد خطط الاشتراك: " . $result['count'] . "\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM coupons");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "  📊 عدد الكوبونات: " . $result['count'] . "\n";
    
    // ============================================================================
    // 6. ملخص النتائج
    // ============================================================================
    echo "\n" . str_repeat("=", 80) . "\n";
    echo "📋 ملخص التحقق:\n";
    echo "  • عدد الجداول المطلوبة: " . count($requiredTables) . "\n";
    echo "  • عدد الأعمدة المطلوبة: " . count($requiredColumns) . "\n";
    echo "  • عدد الأعمدة الناقصة: " . count($missingColumns) . "\n";
    echo "  • عدد الفهارس المطلوبة: " . count($requiredIndexes) . "\n";
    
    if (empty($missingColumns)) {
        echo "\n✅ قاعدة البيانات جاهزة للاستخدام!\n";
    } else {
        echo "\n⚠️  يوجد أعمدة ناقصة: " . implode(', ', $missingColumns) . "\n";
        echo "💡 يرجى تشغيل سكريبت التحديث: database/update_subscription_database.php\n";
    }
    
    echo "\n" . str_repeat("=", 80) . "\n";
    
} catch (Exception $e) {
    echo "\n❌ خطأ أثناء التحقق:\n";
    echo $e->getMessage() . "\n";
    exit(1);
}
?>

