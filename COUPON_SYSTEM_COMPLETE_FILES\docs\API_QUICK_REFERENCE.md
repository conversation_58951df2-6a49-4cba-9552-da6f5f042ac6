# 📡 المرجع السريع - واجهات API
# Quick Reference - API Endpoints

## 🎯 نقاط النهاية السريعة | Quick Endpoints

### 1️⃣ حساب السعر
```
POST api/subscription_coupon_api.php?action=calculate_price
plan_id=1&coupon_code=SAVE10
```
**الاستجابة:** base_price, coupon_discount, final_price

---

### 2️⃣ إنشاء اشتراك
```
POST api/subscription_coupon_api.php?action=create_with_coupon
plan_id=1&player_id=123&coupon_code=SAVE10
```
**الاستجابة:** subscription_id, subscription data

---

### 3️⃣ تجديد الاشتراك
```
POST api/subscription_coupon_api.php?action=renew_with_coupon
subscription_id=456&coupon_code=RENEW20
```
**الاستجابة:** subscription_id, subscription data

---

### 4️⃣ ترقية الاشتراك
```
POST api/subscription_coupon_api.php?action=upgrade_with_coupon
subscription_id=456&new_plan_id=2&coupon_code=UPGRADE15
```
**الاستجابة:** subscription_id, subscription data

---

### 5️⃣ إزالة الكوبون
```
POST api/subscription_coupon_api.php?action=remove_coupon
subscription_id=456
```
**الاستجابة:** subscription data

---

### 6️⃣ التحقق من الكوبون
```
POST api/subscription_coupon_api.php?action=validate_coupon
coupon_code=SAVE10&plan_id=1
```
**الاستجابة:** valid, coupon data

---

### 7️⃣ الحصول على الاشتراك
```
POST api/subscription_coupon_api.php?action=get_subscription
subscription_id=456
```
**الاستجابة:** subscription data

---

### 8️⃣ قائمة الاشتراكات
```
POST api/subscription_coupon_api.php?action=list_subscriptions
player_id=123
```
**الاستجابة:** subscriptions array, count

---

## 💻 أمثلة JavaScript

### مثال 1: حساب السعر
```javascript
const data = new FormData();
data.append('plan_id', 1);
data.append('coupon_code', 'SAVE10');

fetch('api/subscription_coupon_api.php?action=calculate_price', {
  method: 'POST',
  body: data
})
.then(r => r.json())
.then(d => console.log(d.final_price));
```

### مثال 2: إنشاء اشتراك
```javascript
const data = new FormData();
data.append('plan_id', 1);
data.append('player_id', 123);
data.append('coupon_code', 'SAVE10');

fetch('api/subscription_coupon_api.php?action=create_with_coupon', {
  method: 'POST',
  body: data
})
.then(r => r.json())
.then(d => console.log(d.subscription_id));
```

### مثال 3: تجديد الاشتراك
```javascript
const data = new FormData();
data.append('subscription_id', 456);
data.append('coupon_code', 'RENEW20');

fetch('api/subscription_coupon_api.php?action=renew_with_coupon', {
  method: 'POST',
  body: data
})
.then(r => r.json())
.then(d => console.log(d.success));
```

---

## 💻 أمثلة PHP

### مثال 1: حساب السعر
```php
$data = [
    'plan_id' => 1,
    'coupon_code' => 'SAVE10'
];

$response = callAPI('calculate_price', $data);
echo $response['final_price'];
```

### مثال 2: إنشاء اشتراك
```php
$data = [
    'plan_id' => 1,
    'player_id' => 123,
    'coupon_code' => 'SAVE10'
];

$response = callAPI('create_with_coupon', $data);
echo $response['subscription_id'];
```

### مثال 3: تجديد الاشتراك
```php
$data = [
    'subscription_id' => 456,
    'coupon_code' => 'RENEW20'
];

$response = callAPI('renew_with_coupon', $data);
echo $response['success'] ? 'تم التجديد' : 'فشل';
```

---

## 🔐 المصادقة | Authentication

جميع النقاط تتطلب:
```php
$_SESSION['user_id'] // يجب أن تكون موجودة
```

---

## ✅ رموز الحالة | Status Codes

| الكود | المعنى |
|------|--------|
| 200 | نجح |
| 400 | خطأ في الطلب |
| 401 | غير مصرح |
| 500 | خطأ في الخادم |

---

## 📊 استجابة النجاح | Success Response

```json
{
  "success": true,
  "data": {...},
  "message": "رسالة النجاح"
}
```

---

## ❌ استجابة الخطأ | Error Response

```json
{
  "success": false,
  "error": "رسالة الخطأ",
  "timestamp": "2025-12-28 10:30:00"
}
```

---

**تاريخ الإنشاء:** 2025-12-28
**الإصدار:** 1.0
**الحالة:** ✅ جاهز للاستخدام

