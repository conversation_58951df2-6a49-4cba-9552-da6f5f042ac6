# دليل تكامل الكوبونات مع نظام الدفع
# Coupon Payment Integration Guide

## 📋 نظرة عامة
يوفر هذا الدليل شرحاً شاملاً لكيفية تكامل نظام الكوبونات مع نظام الدفع والفواتير.

## 🔧 المكونات الرئيسية

### 1. فئة CouponPaymentIntegration
**الملف:** `includes/CouponPaymentIntegration.php`

#### الدوال الرئيسية:

```php
// معالجة الدفع مع الكوبون
processPaymentWithCoupon(array $paymentData): array

// تطبيق الكوبون على الفاتورة
applyToInvoice(int $invoiceId, string $couponCode, int $userId): array

// تطبيق الكوبون على الدفعة
applyToPayment(int $paymentId, string $couponCode, int $userId): array

// إلغاء الكوبون من الدفعة
removeCouponFromPayment(int $paymentId): array

// الحصول على بيانات الفاتورة
getInvoice(int $invoiceId): ?array

// الحصول على بيانات الدفعة
getPayment(int $paymentId): ?array
```

## 📊 تحديثات قاعدة البيانات

### جدول invoices
تم إضافة الأعمدة التالية:
- `coupon_id` - معرف الكوبون
- `coupon_code` - كود الكوبون
- `coupon_discount_amount` - مبلغ الخصم
- `final_amount` - المبلغ النهائي

### جدول payments
تم إضافة الأعمدة التالية:
- `coupon_id` - معرف الكوبون
- `coupon_code` - كود الكوبون
- `discount_amount` - مبلغ الخصم
- `final_amount` - المبلغ النهائي
- `updated_at` - آخر تحديث

## 🔌 واجهات API

### 1. معالجة الدفع مع الكوبون
**URL:** `api/coupons_payment_integration.php?action=process_payment_with_coupon`
**Method:** POST

```json
{
  "coupon_code": "SUMMER2024",
  "amount": 500,
  "user_id": 1,
  "plan_id": 1,
  "subscription_id": 1,
  "payment_id": 1
}
```

### 2. تطبيق الكوبون على الفاتورة
**URL:** `api/coupons_payment_integration.php?action=apply_to_invoice`
**Method:** POST

```json
{
  "invoice_id": 1,
  "coupon_code": "SUMMER2024",
  "user_id": 1
}
```

### 3. تطبيق الكوبون على الدفعة
**URL:** `api/coupons_payment_integration.php?action=apply_to_payment`
**Method:** POST

```json
{
  "payment_id": 1,
  "coupon_code": "SUMMER2024",
  "user_id": 1
}
```

### 4. إلغاء الكوبون
**URL:** `api/coupons_payment_integration.php?action=remove_coupon`
**Method:** POST

```json
{
  "payment_id": 1
}
```

### 5. التحقق من صحة الكوبون
**URL:** `api/coupons_payment_integration.php?action=validate_coupon_for_payment`
**Method:** POST

```json
{
  "coupon_code": "SUMMER2024",
  "amount": 500,
  "user_id": 1,
  "plan_id": 1
}
```

### 6. حساب الخصم
**URL:** `api/coupons_payment_integration.php?action=calculate_discount`
**Method:** POST

```json
{
  "coupon_code": "SUMMER2024",
  "amount": 500
}
```

## 🎯 حالات الاستخدام

### حالة 1: تطبيق الكوبون أثناء الدفع
1. المستخدم يدخل كود الكوبون
2. يتم التحقق من صحة الكوبون
3. يتم حساب الخصم
4. يتم تحديث المبلغ النهائي
5. يتم معالجة الدفع

### حالة 2: تطبيق الكوبون على فاتورة موجودة
1. الإدارة تختار فاتورة
2. تختار كوبون
3. يتم تطبيق الكوبون
4. يتم تحديث بيانات الفاتورة

## 📈 التقارير والإحصائيات

يمكن الحصول على إحصائيات الكوبونات من خلال:
- عدد مرات الاستخدام
- إجمالي الخصم المطبق
- الإيرادات بعد الخصم
- معدل التحويل

## ⚠️ ملاحظات مهمة

1. **التحقق من الصلاحيات:** تأكد من التحقق من صلاحيات المستخدم
2. **التحقق من الصحة:** تحقق دائماً من صحة الكوبون قبل التطبيق
3. **تسجيل العمليات:** جميع العمليات يتم تسجيلها في جدول coupon_usage
4. **الأمان:** استخدم prepared statements لمنع SQL injection

## 🔐 الأمان

- جميع المدخلات يتم التحقق منها
- استخدام prepared statements
- تسجيل جميع العمليات
- التحقق من الصلاحيات

## 📞 الدعم

للمزيد من المعلومات، راجع:
- `docs/COUPON_SYSTEM_DOCUMENTATION.md`
- `includes/CouponPaymentIntegration.php`
- `api/coupons_payment_integration.php`

