# دليل المرجع السريع
# Quick Reference Guide

## 🚀 البدء السريع | Quick Start

### استيراد الفئات
```php
require_once 'includes/CouponManager.php';
require_once 'includes/CouponValidator.php';
require_once 'includes/CouponPaymentIntegration.php';
require_once 'includes/CouponSubscriptionIntegration.php';
require_once 'includes/CouponLoyaltyIntegration.php';
require_once 'includes/CouponReportingSystem.php';
```

### إنشاء الكائنات
```php
$couponManager = new CouponManager($pdo);
$couponValidator = new CouponValidator($pdo, $couponManager);
$paymentIntegration = new CouponPaymentIntegration($pdo, $couponManager, $couponValidator);
$subscriptionIntegration = new CouponSubscriptionIntegration($pdo, $couponManager, $couponValidator);
$loyaltyIntegration = new CouponLoyaltyIntegration($pdo, $couponManager);
$reportingSystem = new CouponReportingSystem($pdo);
```

---

## 📝 العمليات الشائعة | Common Operations

### 1. إنشاء كوبون
```php
$result = $couponManager->createCoupon([
    'code' => 'SUMMER20',
    'name' => 'عرض الصيف',
    'type' => 'percentage',
    'value' => 20,
    'minimum_amount' => 100,
    'usage_limit' => 100,
    'usage_limit_per_user' => 5,
    'valid_from' => date('Y-m-d'),
    'valid_until' => date('Y-m-d', strtotime('+30 days')),
    'is_active' => 1
]);
```

### 2. التحقق من الكوبون
```php
$validation = $couponValidator->validate('SUMMER20', [
    'amount' => 500,
    'user_id' => 123,
    'plan_id' => 1
]);

if ($validation['valid']) {
    echo "الكوبون صحيح";
} else {
    echo "خطأ: " . $validation['error'];
}
```

### 3. حساب الخصم
```php
$coupon = $couponManager->getCouponByCode('SUMMER20');
$discount = $couponManager->calculateDiscount($coupon, 500);
echo "الخصم: " . $discount;
```

### 4. معالجة الدفع مع الكوبون
```php
$result = $paymentIntegration->processPaymentWithCoupon([
    'coupon_code' => 'SUMMER20',
    'amount' => 500,
    'user_id' => 123,
    'plan_id' => 1
]);

echo "المبلغ النهائي: " . $result['final_amount'];
```

### 5. تطبيق الكوبون على الاشتراك
```php
$result = $subscriptionIntegration->applyToNewSubscription([
    'coupon_code' => 'SUMMER20',
    'plan_id' => 1,
    'user_id' => 123,
    'player_id' => 456
]);

echo "معرف الاشتراك: " . $result['subscription_id'];
```

### 6. منح نقاط الولاء
```php
$result = $loyaltyIntegration->awardLoyaltyPoints(
    $couponId,
    $userId,
    $discountAmount
);

echo "النقاط الممنوحة: " . $result['points_awarded'];
```

### 7. استبدال الكوبون بالنقاط
```php
$result = $loyaltyIntegration->redeemCouponWithLoyaltyPoints(
    $couponId,
    $userId
);

echo "الكوبون: " . $result['coupon_code'];
```

### 8. الحصول على التقارير
```php
$usageReport = $reportingSystem->getUsageReport([
    'start_date' => '2025-01-01',
    'end_date' => '2025-01-31'
]);

$revenueReport = $reportingSystem->getRevenueReport([
    'start_date' => '2025-01-01',
    'end_date' => '2025-01-31'
]);
```

---

## 🔌 نقاط النهاية API | API Endpoints

### التحقق من الكوبون
```
POST /api/coupons_api.php?action=validate
Parameters: code, amount, user_id (optional), plan_id (optional)
```

### تطبيق الكوبون
```
POST /api/coupons_api.php?action=apply
Parameters: coupon_id, user_id, amount, subscription_id (optional), payment_id (optional)
```

### الحصول على الكوبون
```
GET /api/coupons_api.php?action=get&id=1
```

### قائمة الكوبونات
```
GET /api/coupons_api.php?action=list&page=1&limit=20
```

### إنشاء كوبون (Admin)
```
POST /api/coupons_api.php?action=create
Parameters: code, name, type, value, minimum_amount, usage_limit, valid_from, valid_until, is_active
```

### تحديث الكوبون (Admin)
```
POST /api/coupons_api.php?action=update
Parameters: id + (same as create)
```

### حذف الكوبون (Admin)
```
POST /api/coupons_api.php?action=delete
Parameters: id
```

### تقرير الاستخدام (Admin)
```
GET /api/coupons_api.php?action=usage_report
Parameters: coupon_id (optional), start_date (optional), end_date (optional)
```

---

## 🗄️ جداول قاعدة البيانات | Database Tables

### coupons
```sql
SELECT * FROM coupons WHERE code = 'SUMMER20';
```

### coupon_usage
```sql
SELECT * FROM coupon_usage WHERE coupon_id = 1 AND status = 'applied';
```

### coupon_plan_mapping
```sql
SELECT * FROM coupon_plan_mapping WHERE coupon_id = 1;
```

### coupon_loyalty_mapping
```sql
SELECT * FROM coupon_loyalty_mapping WHERE coupon_id = 1;
```

### coupon_audit_log
```sql
SELECT * FROM coupon_audit_log WHERE coupon_id = 1 ORDER BY created_at DESC;
```

---

## ⚙️ الإعدادات | Configuration

### تفعيل/تعطيل الميزات
```php
$config = require 'config/coupon_config.php';

// تفعيل الولاء
$config['loyalty']['enabled'] = true;

// تفعيل التقارير
$config['reporting']['enabled'] = true;

// تفعيل التكامل مع الدفع
$config['integrations']['payment_gateway'] = true;
```

---

## 🔍 البحث والتصفية | Search & Filter

### البحث عن كوبون بالكود
```php
$coupon = $couponManager->getCouponByCode('SUMMER20');
```

### الحصول على كوبون بالمعرف
```php
$coupon = $couponManager->getCoupon(1);
```

### تقرير مع تصفية
```php
$report = $reportingSystem->getUsageReport([
    'coupon_id' => 1,
    'start_date' => '2025-01-01',
    'end_date' => '2025-01-31',
    'is_active' => 1
]);
```

---

## 🐛 معالجة الأخطاء | Error Handling

### التحقق من النتيجة
```php
$result = $couponManager->createCoupon($data);

if ($result['success']) {
    echo "نجح: " . $result['id'];
} else {
    echo "خطأ: " . $result['error'];
}
```

### الأخطاء الشائعة
```
"الكوبون غير موجود"
"انتهت صلاحية الكوبون"
"تم تجاوز حد الاستخدام"
"المبلغ أقل من الحد الأدنى"
"المستخدم غير مؤهل لهذا الكوبون"
```

---

## 📊 الإحصائيات | Statistics

### ملخص الاستخدام
```php
$report = $reportingSystem->getUsageReport();
echo "إجمالي الخصم: " . $report['summary']['total_discount'];
echo "عدد الاستخدامات: " . $report['summary']['total_usage'];
```

### ملخص الإيرادات
```php
$report = $reportingSystem->getRevenueReport();
echo "الإيرادات الإجمالية: " . $report['summary']['total_gross_revenue'];
echo "الإيرادات الصافية: " . $report['summary']['total_net_revenue'];
```

---

## 🔐 الأمان | Security

### التحقق من الصلاحيات
```php
require_roles(['admin', 'super_admin']);
```

### استخدام Prepared Statements
```php
$stmt = $pdo->prepare("SELECT * FROM coupons WHERE code = ?");
$stmt->execute([$code]);
```

### تسجيل الإجراءات
```php
// يتم تسجيل جميع الإجراءات تلقائياً في coupon_audit_log
```

---

## 📞 الدعم | Support

### الملفات المهمة
- `docs/COUPON_SYSTEM_DOCUMENTATION.md` - التوثيق الكامل
- `docs/COUPON_INTEGRATION_GUIDE.md` - دليل التكامل
- `docs/IMPLEMENTATION_CHECKLIST.md` - قائمة التحقق
- `config/coupon_config.php` - الإعدادات

### الاختبارات
```bash
php tests/CouponSystemTest.php
```

### الواجهة الإدارية
```
/admin/coupons.php
```

---

## 💡 نصائح مفيدة | Tips

1. **استخدم الكوبونات الموسمية** للعروض الخاصة
2. **راقب الكوبونات القريبة من الانتهاء** باستخدام التقارير
3. **استخدم نقاط الولاء** لزيادة الاحتفاظ بالعملاء
4. **حلل البيانات** لفهم سلوك المستخدمين
5. **اختبر الكوبونات** قبل النشر

---

**آخر تحديث:** 2025-12-28

