# 🚀 المرجع السريع - تكامل الكوبونات مع الولاء
# Quick Reference - Loyalty Coupon Integration

## 🎯 الاستخدام السريع

### التهيئة
```php
require_once 'includes/CouponManager.php';
require_once 'includes/CouponLoyaltyIntegration.php';

$couponManager = new CouponManager($pdo);
$loyaltyIntegration = new CouponLoyaltyIntegration($pdo, $couponManager);
```

---

## 📌 الدوال الرئيسية

### 1. منح نقاط الولاء
```php
$result = $loyaltyIntegration->awardLoyaltyPoints(
    couponId: 1,
    userId: 123,
    discountAmount: 100
);
// النتيجة: ['success' => true, 'points_awarded' => 10]
```

### 2. استبدال الكوبون بالنقاط
```php
$result = $loyaltyIntegration->redeemCouponWithLoyaltyPoints(
    couponId: 1,
    userId: 123
);
// النتيجة: ['success' => true, 'coupon_code' => 'LOYALTY100', 'remaining_points' => 450]
```

### 3. إنشاء كوبون ولاء
```php
$result = $loyaltyIntegration->createLoyaltyCoupon(
    couponData: [
        'code' => 'LOYALTY100',
        'name' => 'كوبون الولاء',
        'type' => 'fixed',
        'value' => 100,
        'is_active' => 1
    ],
    loyaltyData: [
        'points_required' => 500,
        'points_earned' => 50
    ]
);
// النتيجة: ['success' => true, 'coupon_id' => 1]
```

### 4. حساب النقاط من الخصم
```php
$points = $loyaltyIntegration->calculateLoyaltyPointsFromDiscount(
    discountAmount: 100,
    couponId: 1
);
// النتيجة: 100 (نقطة واحدة لكل وحدة عملة)
```

### 5. الكوبونات المتاحة
```php
$result = $loyaltyIntegration->getAvailableLoyaltyCoupons(
    userId: 123,
    limit: 10
);
// النتيجة: ['success' => true, 'available_coupons' => [...], 'count' => 5]
```

### 6. تطبيق على عملية شراء
```php
$result = $loyaltyIntegration->applyLoyaltyCouponToPurchase(
    couponId: 1,
    userId: 123,
    purchaseAmount: 1000
);
// النتيجة: ['success' => true, 'discount_amount' => 100, 'final_amount' => 900]
```

### 7. سجل المعاملات
```php
$result = $loyaltyIntegration->getUserLoyaltyHistory(
    userId: 123,
    limit: 50,
    offset: 0
);
// النتيجة: ['success' => true, 'transactions' => [...], 'total' => 25]
```

### 8. إحصائيات الولاء
```php
$stats = $loyaltyIntegration->getUserLoyaltyStats(userId: 123);
// النتيجة: ['success' => true, 'total_earned' => 500, 'total_redeemed' => 100, 'current_balance' => 400]
```

---

## 🔌 نقاط نهاية API

### الحصول على النقاط
```
GET /api/loyalty_coupon_api.php?action=get_user_points
```

### الكوبونات المتاحة
```
GET /api/loyalty_coupon_api.php?action=get_available_coupons&limit=10
```

### استبدال الكوبون
```
POST /api/loyalty_coupon_api.php?action=redeem_coupon
Body: { coupon_id: 1 }
```

### تطبيق على الشراء
```
POST /api/loyalty_coupon_api.php?action=apply_loyalty_coupon
Body: { coupon_id: 1, purchase_amount: 1000 }
```

### سجل المعاملات
```
GET /api/loyalty_coupon_api.php?action=get_transaction_history&limit=50&offset=0
```

### حساب النقاط
```
POST /api/loyalty_coupon_api.php?action=calculate_points
Body: { discount_amount: 100, coupon_id: 1 }
```

### منح النقاط (مسؤول)
```
POST /api/loyalty_coupon_api.php?action=award_points
Body: { user_id: 123, points: 100, reason: 'مكافأة خاصة' }
```

### إحصائيات الولاء (مسؤول)
```
GET /api/loyalty_coupon_api.php?action=get_loyalty_stats
```

---

## 🎛️ الإدارة

### صفحة الإدارة
```
/admin/coupon_loyalty_management.php
```

### المميزات:
- إنشاء كوبونات ولاء جديدة
- تحديث ربط الولاء
- عرض الإحصائيات
- إدارة الكوبونات النشطة

---

## 📊 الجداول

### coupon_loyalty_mapping
```sql
SELECT * FROM coupon_loyalty_mapping WHERE coupon_id = 1;
```

### loyalty_points_ledger
```sql
SELECT * FROM loyalty_points_ledger WHERE player_id = 123 ORDER BY created_at DESC;
```

### loyalty_points
```sql
SELECT * FROM loyalty_points WHERE player_id = 123;
```

### loyalty_tiers
```sql
SELECT * FROM loyalty_tiers WHERE status = 'active';
```

---

## 🧪 الاختبارات

### تشغيل الاختبارات
```bash
php tests/test_loyalty_coupon_integration.php
```

### الاختبارات المتاحة:
1. إنشاء كوبون ولاء
2. استبدال الكوبون بالنقاط
3. منح نقاط الولاء
4. حساب النقاط من الخصم
5. الكوبونات المتاحة
6. تطبيق على عملية شراء
7. إحصائيات الولاء
8. سجل المعاملات

---

## 🔐 الأمان

- ✅ التحقق من المصادقة
- ✅ التحقق من الصلاحيات
- ✅ استخدام Prepared Statements
- ✅ معالجة الأخطاء
- ✅ تسجيل المعاملات

---

## 📞 الدعم

للمزيد من المعلومات، راجع:
- `docs/LOYALTY_COUPON_INTEGRATION_GUIDE.md`
- `LOYALTY_COUPON_INTEGRATION_COMPLETION_REPORT.md`

---

**آخر تحديث:** 2025-12-28

