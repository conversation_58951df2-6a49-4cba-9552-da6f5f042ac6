-- =====================================================
-- إنشاء جداول الكوبونات الآمن - بدون مفاتيح خارجية
-- Safe Coupon Tables Creation - Without Foreign Keys
-- =====================================================

-- تعطيل فحص المفاتيح الخارجية مؤقتاً
SET FOREIGN_KEY_CHECKS = 0;

-- 1. جدول الكوبونات الرئيسي
CREATE TABLE IF NOT EXISTS `coupons` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `uuid` CHAR(36) UNIQUE NOT NULL,
  `code` VARCHAR(50) UNIQUE NOT NULL COMMENT 'كود الكوبون الفريد',
  `name` VARCHAR(100) NOT NULL COMMENT 'اسم الكوبون',
  `description` TEXT NULL COMMENT 'وصف الكوبون',
  `type` ENUM('percentage','fixed','free_month','free_session','upgrade') NOT NULL DEFAULT 'percentage' COMMENT 'نوع الخصم',
  `value` DECIMAL(10,2) NOT NULL COMMENT 'قيمة الخصم',
  `minimum_amount` DECIMAL(10,2) DEFAULT 0 COMMENT 'الحد الأدنى للمبلغ',
  `maximum_discount` DECIMAL(10,2) NULL COMMENT 'أقصى خصم',
  `usage_limit` INT UNSIGNED NULL COMMENT 'حد الاستخدام الكلي',
  `usage_limit_per_user` TINYINT UNSIGNED DEFAULT 1 COMMENT 'حد الاستخدام لكل مستخدم',
  `used_count` INT UNSIGNED DEFAULT 0 COMMENT 'عدد مرات الاستخدام',
  `valid_from` DATETIME NOT NULL COMMENT 'تاريخ البداية',
  `valid_until` DATETIME NOT NULL COMMENT 'تاريخ النهاية',
  `is_active` BOOLEAN DEFAULT TRUE COMMENT 'هل الكوبون مفعل',
  `applicable_to` JSON NULL COMMENT 'قابل للتطبيق على (plans, services, etc)',
  `user_restrictions` JSON NULL COMMENT 'قيود المستخدمين (new_members, vip, etc)',
  `excluded_users` JSON NULL COMMENT 'المستخدمون المستثنون',
  `created_by` BIGINT UNSIGNED NULL COMMENT 'من أنشأ الكوبون',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_code` (`code`),
  INDEX `idx_is_active` (`is_active`),
  INDEX `idx_valid_dates` (`valid_from`, `valid_until`),
  INDEX `idx_used_count` (`used_count`),
  INDEX `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الكوبونات الرئيسي';

-- 2. جدول استخدام الكوبونات
CREATE TABLE IF NOT EXISTS `coupon_usage` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `coupon_id` BIGINT UNSIGNED NOT NULL,
  `user_id` BIGINT UNSIGNED NOT NULL,
  `player_id` BIGINT UNSIGNED NULL,
  `subscription_id` BIGINT UNSIGNED NULL,
  `payment_id` BIGINT UNSIGNED NULL,
  `invoice_id` BIGINT UNSIGNED NULL,
  `discount_amount` DECIMAL(10,2) NOT NULL COMMENT 'مبلغ الخصم المطبق',
  `original_amount` DECIMAL(10,2) NOT NULL COMMENT 'المبلغ الأصلي',
  `final_amount` DECIMAL(10,2) NOT NULL COMMENT 'المبلغ النهائي',
  `status` ENUM('applied','cancelled','refunded') DEFAULT 'applied',
  `used_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `cancelled_at` TIMESTAMP NULL,
  `notes` TEXT NULL,
  INDEX `idx_coupon_id` (`coupon_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_player_id` (`player_id`),
  INDEX `idx_subscription_id` (`subscription_id`),
  INDEX `idx_payment_id` (`payment_id`),
  INDEX `idx_used_at` (`used_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='سجل استخدام الكوبونات';

-- 3. جدول ربط الكوبونات بالخطط
CREATE TABLE IF NOT EXISTS `coupon_plan_mapping` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `coupon_id` BIGINT UNSIGNED NOT NULL,
  `plan_id` BIGINT UNSIGNED NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY `unique_coupon_plan` (`coupon_id`, `plan_id`),
  INDEX `idx_coupon_id` (`coupon_id`),
  INDEX `idx_plan_id` (`plan_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ربط الكوبونات بخطط الاشتراك';

-- 4. جدول ربط الكوبونات بنقاط الولاء
CREATE TABLE IF NOT EXISTS `coupon_loyalty_mapping` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `coupon_id` BIGINT UNSIGNED NOT NULL,
  `loyalty_points_required` INT UNSIGNED DEFAULT 0 COMMENT 'نقاط الولاء المطلوبة',
  `loyalty_points_earned` INT UNSIGNED DEFAULT 0 COMMENT 'نقاط الولاء المكتسبة',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY `unique_coupon_loyalty` (`coupon_id`),
  INDEX `idx_coupon_id` (`coupon_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ربط الكوبونات بنقاط الولاء';

-- 5. جدول سجل الكوبونات (للتدقيق)
CREATE TABLE IF NOT EXISTS `coupon_audit_log` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `coupon_id` BIGINT UNSIGNED NOT NULL,
  `action` VARCHAR(50) NOT NULL COMMENT 'الإجراء (create, update, delete, activate, deactivate)',
  `old_values` JSON NULL COMMENT 'القيم القديمة',
  `new_values` JSON NULL COMMENT 'القيم الجديدة',
  `performed_by` BIGINT UNSIGNED NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX `idx_coupon_id` (`coupon_id`),
  INDEX `idx_action` (`action`),
  INDEX `idx_created_at` (`created_at`),
  INDEX `idx_performed_by` (`performed_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='سجل تدقيق الكوبونات';

-- إعادة تفعيل فحص المفاتيح الخارجية
SET FOREIGN_KEY_CHECKS = 1;

-- رسالة نجاح
SELECT 'تم إنشاء جداول الكوبونات بنجاح!' AS message;
