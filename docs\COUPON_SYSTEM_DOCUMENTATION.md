# نظام الكوبونات المتكامل
# Comprehensive Coupon System Documentation

## 📋 نظرة عامة | Overview

نظام كوبونات متقدم ومتكامل يوفر:
- ✅ إدارة شاملة للكوبونات
- ✅ تكامل كامل مع نظام الدفع والفواتير
- ✅ تكامل مع نظام الاشتراكات والتجديد
- ✅ ربط مع نظام نقاط الولاء والمكافآت
- ✅ تقارير شاملة والإحصائيات
- ✅ سجل تدقيق كامل

---

## 🗄️ قاعدة البيانات | Database

### الجداول الرئيسية

#### 1. جدول الكوبونات (coupons)
```sql
- id: معرف فريد
- uuid: معرف عام فريد
- code: كود الكوبون الفريد
- name: اسم الكوبون
- type: نوع الخصم (percentage, fixed, free_month, free_session, upgrade)
- value: قيمة الخصم
- minimum_amount: الحد الأدنى للمبلغ
- maximum_discount: أقصى خصم
- usage_limit: حد الاستخدام الكلي
- usage_limit_per_user: حد الاستخدام لكل مستخدم
- valid_from/valid_until: فترة الصلاحية
- is_active: حالة التفعيل
```

#### 2. جدول استخدام الكوبونات (coupon_usage)
```sql
- coupon_id: معرف الكوبون
- user_id: معرف المستخدم
- subscription_id: معرف الاشتراك
- payment_id: معرف الدفعة
- discount_amount: مبلغ الخصم
- original_amount: المبلغ الأصلي
- final_amount: المبلغ النهائي
- status: حالة الاستخدام (applied, cancelled, refunded)
```

#### 3. جدول ربط الكوبونات بالخطط (coupon_plan_mapping)
```sql
- coupon_id: معرف الكوبون
- plan_id: معرف الخطة
```

#### 4. جدول ربط الكوبونات بالولاء (coupon_loyalty_mapping)
```sql
- coupon_id: معرف الكوبون
- loyalty_points_required: نقاط الولاء المطلوبة
- loyalty_points_earned: نقاط الولاء المكتسبة
```

#### 5. جدول سجل التدقيق (coupon_audit_log)
```sql
- coupon_id: معرف الكوبون
- action: الإجراء (create, update, delete, activate, deactivate)
- old_values: القيم القديمة (JSON)
- new_values: القيم الجديدة (JSON)
- performed_by: من قام بالإجراء
```

---

## 🔧 الفئات الرئيسية | Core Classes

### 1. CouponManager
إدارة دورة حياة الكوبونات

**الدوال الرئيسية:**
- `createCoupon(array $data)` - إنشاء كوبون جديد
- `updateCoupon(int $id, array $data)` - تحديث الكوبون
- `getCoupon(int $id)` - الحصول على الكوبون
- `getCouponByCode(string $code)` - البحث بالكود
- `validateCoupon(string $code, float $amount, ?int $userId)` - التحقق
- `calculateDiscount(array $coupon, float $amount)` - حساب الخصم
- `applyCoupon(int $couponId, int $userId, float $amount)` - تطبيق الكوبون

### 2. CouponValidator
التحقق المتقدم من صحة الكوبونات

**الدوال الرئيسية:**
- `validate(string $code, array $context)` - التحقق الشامل
- `validateDates(array $coupon)` - التحقق من الصلاحية
- `validateUsageLimit(array $coupon)` - التحقق من حد الاستخدام
- `validateUserRestrictions(array $coupon, array $context)` - التحقق من القيود

### 3. CouponPaymentIntegration
تكامل الكوبونات مع نظام الدفع

**الدوال الرئيسية:**
- `processPaymentWithCoupon(array $paymentData)` - معالجة الدفع
- `applyToInvoice(int $invoiceId, string $couponCode, int $userId)` - تطبيق على الفاتورة
- `applyToPayment(int $paymentId, string $couponCode, int $userId)` - تطبيق على الدفعة
- `removeCouponFromPayment(int $paymentId)` - إلغاء الكوبون

### 4. CouponSubscriptionIntegration
تكامل الكوبونات مع نظام الاشتراكات

**الدوال الرئيسية:**
- `calculateSubscriptionPrice(int $planId, ?string $couponCode, int $userId)` - حساب السعر
- `applyToNewSubscription(array $subscriptionData)` - تطبيق على اشتراك جديد
- `applyToRenewal(int $subscriptionId, ?string $couponCode)` - تطبيق على التجديد

### 5. CouponLoyaltyIntegration
تكامل الكوبونات مع نظام الولاء

**الدوال الرئيسية:**
- `awardLoyaltyPoints(int $couponId, int $userId, float $discountAmount)` - منح النقاط
- `redeemCouponWithLoyaltyPoints(int $couponId, int $userId)` - استبدال بالنقاط
- `createLoyaltyCoupon(array $couponData, array $loyaltyData)` - إنشاء كوبون ولاء

### 6. CouponReportingSystem
نظام التقارير والإحصائيات

**الدوال الرئيسية:**
- `getUsageReport(array $filters)` - تقرير الاستخدام
- `getRevenueReport(array $filters)` - تقرير الإيرادات
- `getTopCouponsReport(int $limit)` - أفضل الكوبونات
- `getTopUsersReport(int $limit)` - أكثر المستخدمين استخداماً
- `getExpiredCouponsReport()` - الكوبونات المنتهية
- `getExpiringCouponsReport(int $daysThreshold)` - الكوبونات القريبة من الانتهاء

---

## 🌐 واجهات API | API Endpoints

### Base URL
```
/api/coupons_api.php
```

### الإجراءات المتاحة

#### 1. التحقق من الكوبون
```
POST /api/coupons_api.php?action=validate
Parameters:
- code: كود الكوبون
- amount: المبلغ
- user_id: معرف المستخدم (اختياري)
- plan_id: معرف الخطة (اختياري)
```

#### 2. تطبيق الكوبون
```
POST /api/coupons_api.php?action=apply
Parameters:
- coupon_id: معرف الكوبون
- user_id: معرف المستخدم
- amount: المبلغ
- subscription_id: معرف الاشتراك (اختياري)
- payment_id: معرف الدفعة (اختياري)
```

#### 3. الحصول على الكوبون
```
GET /api/coupons_api.php?action=get&id=1
```

#### 4. قائمة الكوبونات
```
GET /api/coupons_api.php?action=list&page=1&limit=20
```

#### 5. إنشاء كوبون (Admin)
```
POST /api/coupons_api.php?action=create
Parameters:
- code, name, description, type, value
- minimum_amount, maximum_discount
- usage_limit, usage_limit_per_user
- valid_from, valid_until, is_active
```

#### 6. تحديث الكوبون (Admin)
```
POST /api/coupons_api.php?action=update
Parameters: (same as create + id)
```

#### 7. حذف الكوبون (Admin)
```
POST /api/coupons_api.php?action=delete
Parameters:
- id: معرف الكوبون
```

#### 8. تقرير الاستخدام (Admin)
```
GET /api/coupons_api.php?action=usage_report
Parameters:
- coupon_id: معرف الكوبون (اختياري)
- start_date: تاريخ البداية (اختياري)
- end_date: تاريخ النهاية (اختياري)
```

---

## 💡 أمثلة الاستخدام | Usage Examples

### مثال 1: التحقق من الكوبون
```php
$couponValidator = new CouponValidator($pdo, $couponManager);
$result = $couponValidator->validate('SUMMER20', [
    'amount' => 500,
    'user_id' => 123,
    'plan_id' => 1
]);

if ($result['valid']) {
    echo "الكوبون صحيح";
} else {
    echo "خطأ: " . $result['error'];
}
```

### مثال 2: تطبيق الكوبون على الدفع
```php
$paymentIntegration = new CouponPaymentIntegration($pdo, $couponManager, $couponValidator);
$result = $paymentIntegration->processPaymentWithCoupon([
    'coupon_code' => 'SUMMER20',
    'amount' => 500,
    'user_id' => 123,
    'plan_id' => 1
]);

echo "المبلغ النهائي: " . $result['final_amount'];
```

### مثال 3: تطبيق الكوبون على الاشتراك
```php
$subscriptionIntegration = new CouponSubscriptionIntegration($pdo, $couponManager, $couponValidator);
$result = $subscriptionIntegration->applyToNewSubscription([
    'coupon_code' => 'SUMMER20',
    'plan_id' => 1,
    'user_id' => 123,
    'player_id' => 456
]);

echo "معرف الاشتراك: " . $result['subscription_id'];
```

### مثال 4: استبدال الكوبون بنقاط الولاء
```php
$loyaltyIntegration = new CouponLoyaltyIntegration($pdo, $couponManager);
$result = $loyaltyIntegration->redeemCouponWithLoyaltyPoints(1, 123);

if ($result['success']) {
    echo "تم استبدال الكوبون: " . $result['coupon_code'];
}
```

### مثال 5: الحصول على التقارير
```php
$reportingSystem = new CouponReportingSystem($pdo);

// تقرير الاستخدام
$usageReport = $reportingSystem->getUsageReport([
    'start_date' => '2025-01-01',
    'end_date' => '2025-01-31'
]);

// تقرير الإيرادات
$revenueReport = $reportingSystem->getRevenueReport([
    'start_date' => '2025-01-01',
    'end_date' => '2025-01-31'
]);

// أفضل الكوبونات
$topCoupons = $reportingSystem->getTopCouponsReport(10);
```

---

## 🔐 الأمان | Security

- ✅ التحقق من الصلاحيات (Admin فقط)
- ✅ استخدام Prepared Statements
- ✅ تسجيل جميع الإجراءات (Audit Log)
- ✅ التحقق من صحة البيانات
- ✅ حماية من SQL Injection
- ✅ تشفير البيانات الحساسة

---

## 📊 التقارير المتاحة | Available Reports

1. **تقرير الاستخدام** - عدد مرات استخدام كل كوبون
2. **تقرير الإيرادات** - الخصومات والإيرادات اليومية
3. **أفضل الكوبونات** - الكوبونات الأكثر استخداماً
4. **أكثر المستخدمين** - المستخدمون الذين يستخدمون الكوبونات أكثر
5. **الكوبونات المنتهية** - الكوبونات التي انتهت صلاحيتها
6. **الكوبونات القريبة من الانتهاء** - الكوبونات التي تنتهي قريباً

---

## 🚀 البدء السريع | Quick Start

1. **تشغيل قاعدة البيانات:**
```bash
mysql -u user -p database < database/coupons_system_schema.sql
```

2. **استخدام الفئات:**
```php
require_once 'includes/CouponManager.php';
require_once 'includes/CouponValidator.php';

$couponManager = new CouponManager($pdo);
$couponValidator = new CouponValidator($pdo, $couponManager);
```

3. **الوصول إلى الواجهة الإدارية:**
```
/admin/coupons.php
```

---

## 📞 الدعم | Support

للمزيد من المعلومات أو الدعم، يرجى التواصل مع فريق التطوير.

