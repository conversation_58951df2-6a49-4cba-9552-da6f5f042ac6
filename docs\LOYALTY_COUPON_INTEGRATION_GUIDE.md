# 🎁 دليل تكامل الكوبونات مع نظام الولاء
# Loyalty Coupon Integration Guide

## 📋 المحتويات | Table of Contents

1. [نظرة عامة](#نظرة-عامة)
2. [المميزات](#المميزات)
3. [البنية التحتية](#البنية-التحتية)
4. [الاستخدام](#الاستخدام)
5. [أمثلة عملية](#أمثلة-عملية)
6. [API](#api)
7. [الإدارة](#الإدارة)

---

## 🎯 نظرة عامة

نظام متكامل يربط الكوبونات مع نقاط الولاء، مما يسمح بـ:
- منح نقاط الولاء عند استخدام الكوبونات
- استبدال نقاط الولاء بكوبونات خصم
- حساب النقاط بناءً على قيمة الخصم
- تطبيق مضاعفات النقاط

---

## ✨ المميزات

### 1. منح نقاط الولاء
```php
$result = $loyaltyIntegration->awardLoyaltyPoints(
    couponId: 1,
    userId: 123,
    discountAmount: 100
);
```

### 2. استبدال الكوبون بالنقاط
```php
$result = $loyaltyIntegration->redeemCouponWithLoyaltyPoints(
    couponId: 1,
    userId: 123
);
```

### 3. إنشاء كوبون ولاء
```php
$result = $loyaltyIntegration->createLoyaltyCoupon(
    couponData: [...],
    loyaltyData: [...]
);
```

### 4. حساب النقاط من الخصم
```php
$points = $loyaltyIntegration->calculateLoyaltyPointsFromDiscount(
    discountAmount: 100,
    couponId: 1
);
```

### 5. الكوبونات المتاحة
```php
$result = $loyaltyIntegration->getAvailableLoyaltyCoupons(
    userId: 123,
    limit: 10
);
```

### 6. تطبيق على عملية شراء
```php
$result = $loyaltyIntegration->applyLoyaltyCouponToPurchase(
    couponId: 1,
    userId: 123,
    purchaseAmount: 1000
);
```

### 7. سجل المعاملات
```php
$result = $loyaltyIntegration->getUserLoyaltyHistory(
    userId: 123,
    limit: 50,
    offset: 0
);
```

### 8. إحصائيات الولاء
```php
$stats = $loyaltyIntegration->getUserLoyaltyStats(userId: 123);
```

---

## 🏗️ البنية التحتية

### الجداول الرئيسية

#### 1. coupon_loyalty_mapping
```sql
- id: معرف فريد
- coupon_id: معرف الكوبون
- loyalty_points_required: النقاط المطلوبة للاستبدال
- loyalty_points_earned: النقاط المكتسبة عند الاستخدام
- loyalty_multiplier: مضاعف النقاط
```

#### 2. loyalty_points_ledger
```sql
- id: معرف فريد
- player_id: معرف المستخدم
- source: مصدر النقاط
- delta: التغيير في النقاط
- description: الوصف
- coupon_id: معرف الكوبون (اختياري)
- transaction_type: نوع المعاملة
- created_at: تاريخ الإنشاء
```

#### 3. loyalty_points
```sql
- id: معرف فريد
- player_id: معرف المستخدم
- points_balance: الرصيد الحالي
- total_earned: إجمالي المكتسب
- total_redeemed: إجمالي المستخدم
- last_transaction_at: آخر معاملة
```

#### 4. loyalty_tiers
```sql
- id: معرف فريد
- name: الاسم بالإنجليزية
- name_ar: الاسم بالعربية
- min_points: الحد الأدنى للنقاط
- max_points: الحد الأقصى للنقاط
- multiplier: مضاعف النقاط
- benefits: المزايا (JSON)
```

---

## 💻 الاستخدام

### التهيئة
```php
require_once 'includes/CouponManager.php';
require_once 'includes/CouponLoyaltyIntegration.php';

$couponManager = new CouponManager($pdo);
$loyaltyIntegration = new CouponLoyaltyIntegration($pdo, $couponManager);
```

### إنشاء كوبون ولاء
```php
$couponData = [
    'code' => 'LOYALTY100',
    'name' => 'كوبون الولاء',
    'type' => 'fixed',
    'value' => 100,
    'is_active' => 1,
    'loyalty_multiplier' => 1.5
];

$loyaltyData = [
    'points_required' => 500,
    'points_earned' => 50
];

$result = $loyaltyIntegration->createLoyaltyCoupon($couponData, $loyaltyData);
```

### منح النقاط
```php
$result = $loyaltyIntegration->awardLoyaltyPoints(
    couponId: 1,
    userId: 123,
    discountAmount: 100
);

if ($result['success']) {
    echo "تم منح {$result['points_awarded']} نقطة";
}
```

### استبدال الكوبون
```php
$result = $loyaltyIntegration->redeemCouponWithLoyaltyPoints(1, 123);

if ($result['success']) {
    echo "تم استبدال الكوبون: {$result['coupon_code']}";
    echo "النقاط المتبقية: {$result['remaining_points']}";
}
```

---

## 📚 أمثلة عملية

### مثال 1: عملية شراء كاملة
```php
// 1. التحقق من الكوبون
$coupon = $couponManager->getCoupon(1);

// 2. حساب السعر
$price = $coupon['value'];

// 3. تطبيق الكوبون
$result = $loyaltyIntegration->applyLoyaltyCouponToPurchase(1, 123, 1000);

// 4. معالجة الدفع
$finalAmount = $result['final_amount'];

// 5. تسجيل المعاملة
// ...
```

### مثال 2: لوحة تحكم الولاء
```php
// الحصول على إحصائيات المستخدم
$stats = $loyaltyIntegration->getUserLoyaltyStats(123);

// الحصول على الكوبونات المتاحة
$coupons = $loyaltyIntegration->getAvailableLoyaltyCoupons(123, 10);

// الحصول على السجل
$history = $loyaltyIntegration->getUserLoyaltyHistory(123, 50, 0);
```

---

## 🔌 API

### نقاط النهاية الرئيسية

#### 1. GET /api/loyalty_coupon_api.php?action=get_user_points
الحصول على نقاط الولاء للمستخدم

#### 2. GET /api/loyalty_coupon_api.php?action=get_available_coupons
الحصول على الكوبونات المتاحة

#### 3. POST /api/loyalty_coupon_api.php?action=redeem_coupon
استبدال كوبون بالنقاط

#### 4. POST /api/loyalty_coupon_api.php?action=apply_loyalty_coupon
تطبيق كوبون على عملية شراء

#### 5. GET /api/loyalty_coupon_api.php?action=get_transaction_history
الحصول على سجل المعاملات

---

## 🎛️ الإدارة

### صفحة الإدارة
```
/admin/coupon_loyalty_management.php
```

### المميزات:
- إنشاء كوبونات ولاء جديدة
- تحديث ربط الولاء
- عرض الإحصائيات
- إدارة الكوبونات النشطة

---

## 📊 الإحصائيات

### المقاييس المتاحة
- إجمالي كوبونات الولاء
- الكوبونات النشطة
- النقاط الممنوحة
- النقاط المستخدمة
- متوسط النقاط لكل مستخدم

---

## ✅ قائمة التحقق

- ✅ تثبيت قاعدة البيانات
- ✅ تفعيل الفئات
- ✅ إنشاء الكوبونات
- ✅ اختبار المميزات
- ✅ تفعيل الإدارة

---

**آخر تحديث:** 2025-12-28

