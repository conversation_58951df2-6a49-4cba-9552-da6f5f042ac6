<?php
/**
 * إعدادات قاعدة البيانات - نظام الكوبونات
 * Database Configuration - Coupon System
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'c7c_wolves7c');
define('DB_USER', 'c7c_abuode');
define('DB_PASS', 'Zd<PERSON>haker@14');
define('DB_CHARSET', 'utf8mb4');

/**
 * الحصول على اتصال قاعدة البيانات
 * @return PDO
 */
function getDBConnection() {
    static $pdo = null;
    
    if ($pdo === null) {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET . " COLLATE " . DB_CHARSET . "_unicode_ci"
            ];
            $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
            
            // تعيين ترميز UTF-8 لقاعدة البيانات
            $pdo->exec("SET CHARACTER SET " . DB_CHARSET);
            $pdo->exec("SET COLLATION_CONNECTION = " . DB_CHARSET . "_unicode_ci");
            
        } catch (PDOException $e) {
            error_log("فشل الاتصال بقاعدة البيانات: " . $e->getMessage());
            throw new Exception("فشل في الاتصال بقاعدة البيانات: " . $e->getMessage());
        }
    }
    
    return $pdo;
}

// إنشاء متغير عام للاستخدام
try {
    $pdo = getDBConnection();
} catch (Exception $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}
?>
