# دليل الاختبار الشامل
## Comprehensive Testing Guide

### 📋 نظرة عامة

هذا الدليل يوضح كيفية اختبار جميع مكونات نظام الحضور المتقدم لضمان الاستقرار والجودة.

---

## 🧪 أنواع الاختبارات

### 1. اختبارات الوظائف الأساسية (Functional Testing)

#### 1.1 نظام تسجيل الدخول
- [ ] تسجيل دخول مدير بنجاح
- [ ] تسجيل دخول مدرب بنجاح
- [ ] تسجيل دخول لاعب بنجاح
- [ ] رفض بيانات دخول خاطئة
- [ ] تسجيل الخروج بنجاح
- [ ] حماية CSRF تعمل
- [ ] Rate limiting يعمل

#### 1.2 نظام الحضور
- [ ] تسجيل حضور بالـ QR Code
- [ ] تسجيل حضور بالكاميرا
- [ ] تسجيل حضور يدوي
- [ ] تسجيل انصراف
- [ ] تحديث حالة الحضور
- [ ] عرض سجل الحضور
- [ ] تصدير تقرير الحضور

#### 1.3 نظام نقاط الولاء
- [ ] منح نقاط للحضور
- [ ] منح نقاط للإنجازات
- [ ] استبدال النقاط
- [ ] عرض سجل النقاط
- [ ] حساب النقاط بشكل صحيح

#### 1.4 نظام التقارير
- [ ] تقرير الحضور الشامل
- [ ] تقرير الأداء اليومي
- [ ] تقرير نقاط الولاء
- [ ] تقرير المدربين
- [ ] تصدير CSV
- [ ] تصدير JSON
- [ ] الرسوم البيانية تعمل

#### 1.5 نظام الإشعارات
- [ ] إنشاء إشعار جديد
- [ ] عرض الإشعارات
- [ ] تحديد كمقروء
- [ ] تحديد الكل كمقروء
- [ ] عدد الإشعارات غير المقروءة صحيح
- [ ] الإشعارات المنبثقة تعمل

---

### 2. اختبارات الأمان (Security Testing)

#### 2.1 CSRF Protection
```bash
# اختبار بدون CSRF token
curl -X POST http://localhost/qr_checkin.php \
  -d "player_id=1" \
  -b "session_cookie"
# النتيجة المتوقعة: رفض الطلب
```

#### 2.2 Rate Limiting
```bash
# إرسال 100 طلب متتالي
for i in {1..100}; do
  curl http://localhost/api/face_attendance.php
done
# النتيجة المتوقعة: رفض بعد الحد المسموح
```

#### 2.3 SQL Injection
```sql
-- محاولة حقن SQL
username: admin' OR '1'='1
password: anything
-- النتيجة المتوقعة: رفض الدخول
```

#### 2.4 XSS Protection
```html
<!-- محاولة حقن JavaScript -->
<script>alert('XSS')</script>
<!-- النتيجة المتوقعة: تنظيف الإدخال -->
```

---

### 3. اختبارات الأداء (Performance Testing)

#### 3.1 سرعة الاستعلامات
```sql
-- اختبار استعلام الحضور
EXPLAIN SELECT * FROM active_attendance WHERE training_date = CURDATE();
-- النتيجة المتوقعة: استخدام الفهارس

-- اختبار استعلام الإحصائيات
EXPLAIN SELECT * FROM monthly_attendance_stats;
-- النتيجة المتوقعة: < 100ms
```

#### 3.2 اختبار الحمل
```bash
# استخدام Apache Bench
ab -n 1000 -c 10 http://localhost/index.php
# النتيجة المتوقعة: > 100 requests/sec
```

#### 3.3 حجم قاعدة البيانات
```sql
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
FROM information_schema.TABLES
WHERE table_schema = 'your_database'
ORDER BY size_mb DESC;
```

---

### 4. اختبارات التوافق (Compatibility Testing)

#### 4.1 المتصفحات
- [ ] Chrome (آخر إصدار)
- [ ] Firefox (آخر إصدار)
- [ ] Safari (آخر إصدار)
- [ ] Edge (آخر إصدار)
- [ ] متصفحات الهواتف

#### 4.2 الأجهزة
- [ ] سطح المكتب (1920x1080)
- [ ] اللابتوب (1366x768)
- [ ] التابلت (768x1024)
- [ ] الهاتف (375x667)
- [ ] الهاتف الكبير (414x896)

#### 4.3 أنظمة التشغيل
- [ ] Windows 10/11
- [ ] macOS
- [ ] Linux (Ubuntu)
- [ ] Android
- [ ] iOS

---

### 5. اختبارات واجهة المستخدم (UI/UX Testing)

#### 5.1 التصميم المتجاوب
- [ ] القوائم تعمل على الهواتف
- [ ] الجداول قابلة للتمرير
- [ ] الأزرار بحجم مناسب للمس
- [ ] النصوص واضحة وقابلة للقراءة
- [ ] الصور تتكيف مع الشاشة

#### 5.2 إمكانية الوصول (Accessibility)
- [ ] دعم قارئ الشاشة
- [ ] التنقل بلوحة المفاتيح
- [ ] تباين الألوان مناسب
- [ ] حجم الخط قابل للتعديل
- [ ] النصوص البديلة للصور

#### 5.3 تجربة المستخدم
- [ ] سهولة التنقل
- [ ] رسائل الخطأ واضحة
- [ ] التأكيدات للعمليات الحساسة
- [ ] التحميل السريع
- [ ] الرسوم المتحركة سلسة

---

### 6. اختبارات التكامل (Integration Testing)

#### 6.1 تكامل قاعدة البيانات
```php
// اختبار الاتصال
$pdo = getDBConnection();
assert($pdo instanceof PDO);

// اختبار الاستعلامات
$stmt = $pdo->query("SELECT 1");
assert($stmt->fetchColumn() === "1");
```

#### 6.2 تكامل الأمان
```php
// اختبار SecurityManager
$security = SecurityManager::getInstance($pdo);
assert($security->generateCSRFToken('test') !== null);
assert($security->checkRateLimit('test') === true);
```

#### 6.3 تكامل التقارير
```php
// اختبار ReportsManager
$reports = new ReportsManager($pdo);
$stats = $reports->getSystemStatistics();
assert(isset($stats['total_players']));
```

---

## 🔍 سيناريوهات الاختبار

### سيناريو 1: رحلة المدير الكاملة
1. تسجيل الدخول كمدير
2. عرض لوحة التحكم
3. إضافة لاعب جديد
4. إضافة مدرب جديد
5. إنشاء مجموعة جديدة
6. تسجيل حضور يدوي
7. عرض التقارير
8. تصدير تقرير CSV
9. تسجيل الخروج

### سيناريو 2: رحلة المدرب الكاملة
1. تسجيل الدخول كمدرب
2. عرض مجموعاته
3. تسجيل حضور بالكاميرا
4. عرض سجل الحضور
5. منح نقاط ولاء
6. عرض الإشعارات
7. تسجيل الخروج

### سيناريو 3: رحلة اللاعب الكاملة
1. تسجيل الدخول كلاعب
2. عرض نقاط الولاء
3. عرض سجل الحضور
4. استبدال النقاط
5. عرض الإشعارات
6. تسجيل الخروج

---

## ✅ قائمة التحقق النهائية

### قبل النشر
- [ ] جميع الاختبارات تمر بنجاح
- [ ] لا توجد أخطاء في السجلات
- [ ] النسخ الاحتياطي يعمل
- [ ] الأمان محدّث
- [ ] الأداء مقبول
- [ ] التوثيق كامل
- [ ] التدريب مكتمل

### بعد النشر
- [ ] مراقبة الأداء
- [ ] مراقبة الأخطاء
- [ ] جمع الملاحظات
- [ ] التحديثات الدورية
- [ ] النسخ الاحتياطي المنتظم

---

## 📊 معايير النجاح

- ✅ نسبة نجاح الاختبارات: > 95%
- ✅ وقت الاستجابة: < 200ms
- ✅ معدل الأخطاء: < 0.1%
- ✅ رضا المستخدمين: > 90%
- ✅ وقت التشغيل: > 99.9%

