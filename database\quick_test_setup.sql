-- =====================================================
-- إعداد سريع لاختبار نظام الكوبونات
-- Quick Setup for Coupon System Testing
-- =====================================================

-- حذ<PERSON> الجداول إذا كانت موجودة
DROP TABLE IF EXISTS `coupon_usage`;
DROP TABLE IF EXISTS `coupons`;

-- إنشاء جدول الكوبونات البسيط
CREATE TABLE `coupons` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `uuid` CHAR(36) UNIQUE NOT NULL,
  `code` VARCHAR(50) UNIQUE NOT NULL,
  `name` VARCHAR(100) NOT NULL,
  `description` TEXT NULL,
  `type` ENUM('percentage','fixed') NOT NULL DEFAULT 'percentage',
  `value` DECIMAL(10,2) NOT NULL,
  `minimum_amount` DECIMAL(10,2) DEFAULT 0,
  `maximum_discount` DECIMAL(10,2) NULL,
  `usage_limit` INT UNSIGNED NULL,
  `usage_limit_per_user` TINYINT UNSIGNED DEFAULT 1,
  `used_count` INT UNSIGNED DEFAULT 0,
  `valid_from` DATETIME NOT NULL,
  `valid_until` DATETIME NOT NULL,
  `is_active` BOOLEAN DEFAULT TRUE,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_code` (`code`),
  INDEX `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول استخدام الكوبونات
CREATE TABLE `coupon_usage` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `coupon_id` BIGINT UNSIGNED NOT NULL,
  `user_id` BIGINT UNSIGNED NOT NULL,
  `player_id` BIGINT UNSIGNED NULL,
  `subscription_id` BIGINT UNSIGNED NULL,
  `payment_id` BIGINT UNSIGNED NULL,
  `discount_amount` DECIMAL(10,2) NOT NULL,
  `original_amount` DECIMAL(10,2) NOT NULL,
  `final_amount` DECIMAL(10,2) NOT NULL,
  `status` ENUM('applied','cancelled','refunded') DEFAULT 'applied',
  `used_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX `idx_coupon_id` (`coupon_id`),
  INDEX `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج كوبون تجريبي بسيط
INSERT INTO `coupons` (
    `uuid`,
    `code`,
    `name`,
    `description`,
    `type`,
    `value`,
    `minimum_amount`,
    `maximum_discount`,
    `usage_limit`,
    `usage_limit_per_user`,
    `used_count`,
    `valid_from`,
    `valid_until`,
    `is_active`
) VALUES (
    UUID(),
    'TEST2025',
    'كوبون اختبار 2025',
    'كوبون تجريبي للاختبار',
    'percentage',
    10.00,
    50.00,
    100.00,
    100,
    1,
    0,
    '2025-01-01 00:00:00',
    '2025-12-31 23:59:59',
    1
) ON DUPLICATE KEY UPDATE
    `name` = VALUES(`name`),
    `description` = VALUES(`description`),
    `updated_at` = NOW();

-- إدراج كوبون ثاني للاختبار
INSERT INTO `coupons` (
    `uuid`,
    `code`,
    `name`,
    `description`,
    `type`,
    `value`,
    `minimum_amount`,
    `usage_limit`,
    `usage_limit_per_user`,
    `used_count`,
    `valid_from`,
    `valid_until`,
    `is_active`
) VALUES (
    UUID(),
    'FIXED50',
    'خصم ثابت 50',
    'كوبون خصم ثابت 50 ريال',
    'fixed',
    50.00,
    100.00,
    50,
    1,
    0,
    '2025-01-01 00:00:00',
    '2025-12-31 23:59:59',
    1
) ON DUPLICATE KEY UPDATE
    `name` = VALUES(`name`),
    `description` = VALUES(`description`),
    `updated_at` = NOW();

-- فحص النتائج
SELECT 'تم إنشاء الجداول وإدراج الكوبونات بنجاح!' AS message;
SELECT COUNT(*) AS total_coupons FROM coupons;
SELECT code, name, type, value FROM coupons WHERE is_active = 1;
