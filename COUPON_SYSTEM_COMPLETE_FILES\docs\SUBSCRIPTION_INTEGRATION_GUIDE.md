# 🎟️ دليل تكامل الكوبونات مع نظام الاشتراكات
# Coupon Subscription Integration Guide

## 📋 نظرة عامة | Overview

هذا الدليل يشرح كيفية تكامل نظام الكوبونات مع نظام الاشتراكات في النظام.

## 🎯 الميزات الرئيسية | Key Features

- ✅ حساب سعر الاشتراك مع الكوبون
- ✅ تطبيق الكوبون على الاشتراكات الجديدة
- ✅ تطبيق الكوبون على تجديد الاشتراكات
- ✅ تطبيق الكوبون على ترقية الاشتراكات
- ✅ إزالة الكوبون من الاشتراك
- ✅ التحقق من صحة الكوبون للاشتراك

## 🔧 المكونات الرئيسية | Main Components

### 1. فئة CouponSubscriptionIntegration
**الملف:** `includes/CouponSubscriptionIntegration.php`

**الوظائف الرئيسية:**
```php
// حساب السعر
calculateSubscriptionPrice(int $planId, ?string $couponCode, int $userId): array

// تطبيق على اشتراك جديد
applyToNewSubscription(array $subscriptionData): array

// تطبيق على التجديد
applyToRenewal(int $subscriptionId, ?string $couponCode): array

// تطبيق على الترقية
applyToUpgrade(int $subscriptionId, int $newPlanId, ?string $couponCode): array

// إزالة الكوبون
removeCoupon(int $subscriptionId): array

// الحصول على الاشتراك
getSubscription(int $subscriptionId): ?array
```

### 2. واجهات API
**الملف:** `api/coupons_subscription_integration.php`

**النقاط النهائية:**
- `calculate_price` - حساب السعر
- `apply_to_new` - تطبيق على اشتراك جديد
- `apply_to_renewal` - تطبيق على التجديد
- `apply_to_upgrade` - تطبيق على الترقية
- `remove_coupon` - إزالة الكوبون
- `validate_for_subscription` - التحقق من الصحة
- `get_subscription` - الحصول على البيانات

### 3. واجهة الإدارة
**الملف:** `admin/coupon_subscription_integration.php`

واجهة سهلة الاستخدام لإدارة الكوبونات والاشتراكات

## 📊 جداول قاعدة البيانات | Database Tables

### player_subscriptions
```sql
- coupon_id (BIGINT) - معرف الكوبون
- coupon_code (VARCHAR) - كود الكوبون
- original_amount (DECIMAL) - المبلغ الأصلي
- coupon_discount_amount (DECIMAL) - مبلغ الخصم
- final_amount (DECIMAL) - المبلغ النهائي
- coupon_applied_at (TIMESTAMP) - وقت التطبيق
- coupon_applied_by (INT) - من طبق الكوبون
```

## 🚀 أمثلة الاستخدام | Usage Examples

### مثال 1: حساب السعر
```php
$result = $subscriptionIntegration->calculateSubscriptionPrice(
    planId: 1,
    couponCode: 'SUMMER20',
    userId: 123
);

if ($result['success']) {
    echo "السعر الأصلي: " . $result['original_price'];
    echo "الخصم: " . $result['discount_amount'];
    echo "السعر النهائي: " . $result['final_price'];
}
```

### مثال 2: إنشاء اشتراك مع كوبون
```php
$subscriptionData = [
    'plan_id' => 1,
    'player_id' => 456,
    'user_id' => 123,
    'coupon_code' => 'SUMMER20'
];

$result = $subscriptionIntegration->applyToNewSubscription($subscriptionData);

if ($result['success']) {
    echo "معرف الاشتراك: " . $result['subscription_id'];
    echo "السعر النهائي: " . $result['final_amount'];
}
```

### مثال 3: تجديد الاشتراك مع كوبون
```php
$result = $subscriptionIntegration->applyToRenewal(
    subscriptionId: 789,
    couponCode: 'RENEWAL10'
);

if ($result['success']) {
    echo "تم التجديد بنجاح";
    echo "السعر النهائي: " . $result['final_amount'];
}
```

### مثال 4: ترقية الاشتراك مع كوبون
```php
$result = $subscriptionIntegration->applyToUpgrade(
    subscriptionId: 789,
    newPlanId: 2,
    couponCode: 'UPGRADE15'
);

if ($result['success']) {
    echo "تم الترقية بنجاح";
}
```

### مثال 5: إزالة الكوبون
```php
$result = $subscriptionIntegration->removeCoupon(subscriptionId: 789);

if ($result['success']) {
    echo "تم إزالة الكوبون بنجاح";
}
```

## 🔌 استخدام واجهات API | API Usage

### حساب السعر
```bash
curl -X POST http://system.c7c.club/api/coupons_subscription_integration.php \
  -d "action=calculate_price" \
  -d "plan_id=1" \
  -d "coupon_code=SUMMER20"
```

### تطبيق على اشتراك جديد
```bash
curl -X POST http://system.c7c.club/api/coupons_subscription_integration.php \
  -d "action=apply_to_new" \
  -d "plan_id=1" \
  -d "player_id=456" \
  -d "coupon_code=SUMMER20"
```

### تطبيق على التجديد
```bash
curl -X POST http://system.c7c.club/api/coupons_subscription_integration.php \
  -d "action=apply_to_renewal" \
  -d "subscription_id=789" \
  -d "coupon_code=RENEWAL10"
```

## 📝 خطوات التثبيت | Installation Steps

### 1. تشغيل الترحيل
```bash
php database/migrate_subscription_coupon_support.php
```

### 2. التحقق من الأعمدة
```sql
DESCRIBE player_subscriptions;
```

### 3. تشغيل الاختبارات
```bash
php tests/test_subscription_integration.php
```

### 4. الوصول إلى واجهة الإدارة
```
http://system.c7c.club/admin/coupon_subscription_integration.php
```

## ✅ قائمة التحقق | Checklist

- [ ] تشغيل سكريبت الترحيل
- [ ] التحقق من أعمدة قاعدة البيانات
- [ ] تشغيل الاختبارات
- [ ] اختبار واجهات API
- [ ] اختبار واجهة الإدارة
- [ ] التحقق من السجلات

## 🐛 استكشاف الأخطاء | Troubleshooting

### المشكلة: الكوبون لا يطبق
**الحل:** تحقق من صحة الكوبون والتاريخ والحد الأقصى للاستخدام

### المشكلة: خطأ في قاعدة البيانات
**الحل:** تأكد من تشغيل سكريبت الترحيل

### المشكلة: خطأ في الصلاحيات
**الحل:** تحقق من دور المستخدم (admin أو super_admin)

## 📞 الدعم | Support

للمزيد من المعلومات، راجع:
- `docs/COUPON_SYSTEM_DOCUMENTATION.md`
- `docs/COUPON_INTEGRATION_GUIDE.md`
- `includes/CouponSubscriptionIntegration.php`

---

**آخر تحديث:** 2025-12-28
**الإصدار:** 1.0

