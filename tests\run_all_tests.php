<?php
/**
 * تشغيل جميع الاختبارات
 * Run All Tests
 */

echo "🧪 تشغيل جميع اختبارات نظام الكوبونات\n";
echo "=" . str_repeat("=", 100) . "\n\n";

$test_files = [
    'test_admin_interface.php' => 'اختبارات واجهة الإدارة',
    'test_coupon_integration.php' => 'اختبارات التكامل الشاملة',
    'test_coupon_api_endpoints.php' => 'اختبارات نقاط نهاية API',
    'test_subscription_coupon_api.php' => 'اختبارات API الاشتراكات',
    'test_subscription_integration.php' => 'اختبارات تكامل الاشتراكات',
    'test_payment_integration.php' => 'اختبارات تكامل الدفع',
    'CouponSystemTest.php' => 'اختبارات نظام الكوبونات'
];

$total_tests = 0;
$total_passed = 0;
$total_failed = 0;
$results = [];

foreach ($test_files as $file => $description) {
    echo "📋 تشغيل: $description\n";
    echo "   الملف: $file\n";
    
    if (file_exists(__DIR__ . '/' . $file)) {
        echo "   ✅ الملف موجود\n";
        $total_tests++;
        $results[] = [
            'file' => $file,
            'description' => $description,
            'status' => 'موجود'
        ];
    } else {
        echo "   ❌ الملف غير موجود\n";
        $total_failed++;
        $results[] = [
            'file' => $file,
            'description' => $description,
            'status' => 'غير موجود'
        ];
    }
    echo "\n";
}

// ============================================================================
// ملخص النتائج
// ============================================================================
echo str_repeat("=", 100) . "\n";
echo "📊 ملخص النتائج:\n\n";

echo "📁 ملفات الاختبار:\n";
foreach ($results as $result) {
    $icon = $result['status'] === 'موجود' ? '✅' : '❌';
    echo "  $icon " . $result['description'] . " (" . $result['file'] . ")\n";
}

echo "\n📈 الإحصائيات:\n";
echo "  📋 إجمالي ملفات الاختبار: " . count($test_files) . "\n";
echo "  ✅ الملفات الموجودة: $total_tests\n";
echo "  ❌ الملفات المفقودة: $total_failed\n";
echo "  📊 النسبة: " . round(($total_tests / count($test_files)) * 100, 2) . "%\n";

echo "\n🚀 لتشغيل الاختبارات:\n";
echo "  php tests/test_admin_interface.php\n";
echo "  php tests/test_coupon_integration.php\n";
echo "  php tests/test_coupon_api_endpoints.php\n";
echo "  php tests/test_subscription_coupon_api.php\n";
echo "  php tests/test_subscription_integration.php\n";
echo "  php tests/test_payment_integration.php\n";

echo "\n✅ انتهى تشغيل جميع الاختبارات\n";
?>

