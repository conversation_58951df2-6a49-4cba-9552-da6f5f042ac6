# 📡 دليل نقاط نهاية API
# API Endpoints Guide

## 🎯 نقاط النهاية الرئيسية | Main Endpoints

### الملف الرئيسي
**`api/subscription_coupon_api.php`**

---

## 📋 نقاط النهاية | Endpoints

### 1️⃣ حساب السعر مع الكوبون
**Endpoint:** `api/subscription_coupon_api.php?action=calculate_price`
**Method:** POST
**المتطلبات:**
- `plan_id` (int) - معرف الخطة
- `coupon_code` (string, optional) - كود الكوبون

**الاستجابة:**
```json
{
  "success": true,
  "plan_id": 1,
  "base_price": 100.00,
  "coupon_discount": 10.00,
  "final_price": 90.00,
  "coupon_info": {...}
}
```

---

### 2️⃣ إنشاء اشتراك جديد مع الكوبون
**Endpoint:** `api/subscription_coupon_api.php?action=create_with_coupon`
**Method:** POST
**المتطلبات:**
- `plan_id` (int) - معرف الخطة
- `player_id` (int) - معرف اللاعب
- `coupon_code` (string, optional) - كود الكوبون

**الاستجابة:**
```json
{
  "success": true,
  "subscription_id": 123,
  "message": "تم إنشاء الاشتراك بنجاح",
  "subscription": {...}
}
```

---

### 3️⃣ تجديد الاشتراك مع الكوبون
**Endpoint:** `api/subscription_coupon_api.php?action=renew_with_coupon`
**Method:** POST
**المتطلبات:**
- `subscription_id` (int) - معرف الاشتراك
- `coupon_code` (string, optional) - كود الكوبون

**الاستجابة:**
```json
{
  "success": true,
  "subscription_id": 123,
  "message": "تم تجديد الاشتراك بنجاح",
  "subscription": {...}
}
```

---

### 4️⃣ ترقية الاشتراك مع الكوبون
**Endpoint:** `api/subscription_coupon_api.php?action=upgrade_with_coupon`
**Method:** POST
**المتطلبات:**
- `subscription_id` (int) - معرف الاشتراك
- `new_plan_id` (int) - معرف الخطة الجديدة
- `coupon_code` (string, optional) - كود الكوبون

**الاستجابة:**
```json
{
  "success": true,
  "subscription_id": 123,
  "message": "تم ترقية الاشتراك بنجاح",
  "subscription": {...}
}
```

---

### 5️⃣ إزالة الكوبون من الاشتراك
**Endpoint:** `api/subscription_coupon_api.php?action=remove_coupon`
**Method:** POST
**المتطلبات:**
- `subscription_id` (int) - معرف الاشتراك

**الاستجابة:**
```json
{
  "success": true,
  "message": "تم إزالة الكوبون بنجاح",
  "subscription": {...}
}
```

---

### 6️⃣ التحقق من صحة الكوبون
**Endpoint:** `api/subscription_coupon_api.php?action=validate_coupon`
**Method:** POST
**المتطلبات:**
- `coupon_code` (string) - كود الكوبون
- `plan_id` (int) - معرف الخطة

**الاستجابة:**
```json
{
  "success": true,
  "valid": true,
  "coupon": {...},
  "message": "الكوبون صحيح"
}
```

---

### 7️⃣ الحصول على بيانات الاشتراك
**Endpoint:** `api/subscription_coupon_api.php?action=get_subscription`
**Method:** POST
**المتطلبات:**
- `subscription_id` (int) - معرف الاشتراك

**الاستجابة:**
```json
{
  "success": true,
  "subscription": {...}
}
```

---

### 8️⃣ الحصول على قائمة الاشتراكات
**Endpoint:** `api/subscription_coupon_api.php?action=list_subscriptions`
**Method:** POST
**المتطلبات:**
- `player_id` (int) - معرف اللاعب

**الاستجابة:**
```json
{
  "success": true,
  "subscriptions": [...],
  "count": 5
}
```

---

## 🔐 المصادقة | Authentication

جميع النقاط تتطلب:
- جلسة نشطة (`$_SESSION['user_id']`)
- تسجيل دخول صحيح

---

## 📊 أمثلة الاستخدام | Usage Examples

### مثال 1: حساب السعر
```javascript
fetch('api/subscription_coupon_api.php?action=calculate_price', {
  method: 'POST',
  body: new FormData({
    plan_id: 1,
    coupon_code: 'SAVE10'
  })
})
.then(r => r.json())
.then(data => console.log(data));
```

### مثال 2: إنشاء اشتراك
```javascript
fetch('api/subscription_coupon_api.php?action=create_with_coupon', {
  method: 'POST',
  body: new FormData({
    plan_id: 1,
    player_id: 123,
    coupon_code: 'SAVE10'
  })
})
.then(r => r.json())
.then(data => console.log(data));
```

---

## ✅ رموز الحالة | Status Codes

| الكود | المعنى |
|------|--------|
| 200 | نجح |
| 400 | خطأ في الطلب |
| 401 | غير مصرح |
| 500 | خطأ في الخادم |

---

**تاريخ الإنشاء:** 2025-12-28
**الإصدار:** 1.0
**الحالة:** ✅ جاهز للاستخدام

