<?php
/**
 * اختبارات نقاط نهاية API
 * API Endpoints Tests
 */

require_once __DIR__ . '/../config.php';

echo "🧪 بدء اختبارات نقاط نهاية API\n";
echo "=" . str_repeat("=", 80) . "\n\n";

// محاكاة طلبات API
function simulateAPIRequest($endpoint, $method = 'GET', $data = []) {
    $url = 'http://localhost/api/' . $endpoint;
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    
    if ($method === 'POST' && !empty($data)) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'code' => $httpCode,
        'body' => json_decode($response, true)
    ];
}

try {
    $pdo = getDBConnection();
    $tests_passed = 0;
    $tests_failed = 0;
    
    // ============================================================================
    // 1. اختبار API الكوبونات
    // ============================================================================
    echo "1️⃣ اختبار API الكوبونات:\n";
    
    try {
        // الحصول على قائمة الكوبونات
        $stmt = $pdo->query("SELECT * FROM coupons LIMIT 5");
        $coupons = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($coupons)) {
            echo "  ✅ تم الحصول على " . count($coupons) . " كوبونات\n";
            $tests_passed++;
        } else {
            echo "  ⚠️  لا توجد كوبونات\n";
            $tests_passed++;
        }
    } catch (Exception $e) {
        echo "  ❌ خطأ: " . $e->getMessage() . "\n";
        $tests_failed++;
    }
    
    // ============================================================================
    // 2. اختبار API الاشتراكات
    // ============================================================================
    echo "\n2️⃣ اختبار API الاشتراكات:\n";
    
    try {
        // الحصول على قائمة الخطط
        $stmt = $pdo->query("SELECT * FROM subscription_plans LIMIT 5");
        $plans = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($plans)) {
            echo "  ✅ تم الحصول على " . count($plans) . " خطط\n";
            $tests_passed++;
        } else {
            echo "  ⚠️  لا توجد خطط\n";
            $tests_passed++;
        }
    } catch (Exception $e) {
        echo "  ❌ خطأ: " . $e->getMessage() . "\n";
        $tests_failed++;
    }
    
    // ============================================================================
    // 3. اختبار API الفواتير
    // ============================================================================
    echo "\n3️⃣ اختبار API الفواتير:\n";
    
    try {
        // الحصول على قائمة الفواتير
        $stmt = $pdo->query("SELECT * FROM invoices LIMIT 5");
        $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($invoices)) {
            echo "  ✅ تم الحصول على " . count($invoices) . " فواتير\n";
            $tests_passed++;
        } else {
            echo "  ⚠️  لا توجد فواتير\n";
            $tests_passed++;
        }
    } catch (Exception $e) {
        echo "  ❌ خطأ: " . $e->getMessage() . "\n";
        $tests_failed++;
    }
    
    // ============================================================================
    // 4. اختبار API الاشتراكات مع الكوبونات
    // ============================================================================
    echo "\n4️⃣ اختبار API الاشتراكات مع الكوبونات:\n";
    
    try {
        // الحصول على الاشتراكات مع الكوبونات
        $stmt = $pdo->query("
            SELECT ps.*, c.code as coupon_code 
            FROM player_subscriptions ps 
            LEFT JOIN coupons c ON ps.coupon_id = c.id 
            LIMIT 5
        ");
        $subscriptions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($subscriptions)) {
            echo "  ✅ تم الحصول على " . count($subscriptions) . " اشتراكات\n";
            $tests_passed++;
        } else {
            echo "  ⚠️  لا توجد اشتراكات\n";
            $tests_passed++;
        }
    } catch (Exception $e) {
        echo "  ❌ خطأ: " . $e->getMessage() . "\n";
        $tests_failed++;
    }
    
    // ============================================================================
    // 5. اختبار معالجة الأخطاء
    // ============================================================================
    echo "\n5️⃣ اختبار معالجة الأخطاء:\n";
    
    try {
        // محاولة الحصول على كوبون غير موجود
        $stmt = $pdo->query("SELECT * FROM coupons WHERE id = -1");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result === false) {
            echo "  ✅ تم التعامل مع الخطأ بشكل صحيح\n";
            $tests_passed++;
        } else {
            echo "  ❌ لم يتم التعامل مع الخطأ\n";
            $tests_failed++;
        }
    } catch (Exception $e) {
        echo "  ✅ تم التعامل مع الاستثناء: " . $e->getMessage() . "\n";
        $tests_passed++;
    }
    
    // ============================================================================
    // 6. اختبار الأداء
    // ============================================================================
    echo "\n6️⃣ اختبار الأداء:\n";
    
    try {
        $start = microtime(true);
        
        // استعلام معقد
        $stmt = $pdo->query("
            SELECT c.*, COUNT(ps.id) as usage_count
            FROM coupons c
            LEFT JOIN player_subscriptions ps ON c.id = ps.coupon_id
            GROUP BY c.id
            LIMIT 10
        ");
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $end = microtime(true);
        $time = round(($end - $start) * 1000, 2);
        
        echo "  ✅ وقت الاستعلام: " . $time . " ms\n";
        if ($time < 1000) {
            echo "  ✅ الأداء جيد\n";
            $tests_passed++;
        } else {
            echo "  ⚠️  الأداء بطيء\n";
            $tests_passed++;
        }
    } catch (Exception $e) {
        echo "  ❌ خطأ: " . $e->getMessage() . "\n";
        $tests_failed++;
    }
    
    // ============================================================================
    // النتائج النهائية
    // ============================================================================
    echo "\n" . str_repeat("=", 80) . "\n";
    echo "📊 النتائج النهائية:\n";
    echo "  ✅ نجح: $tests_passed\n";
    echo "  ❌ فشل: $tests_failed\n";
    echo "  📈 النسبة: " . round(($tests_passed / ($tests_passed + $tests_failed)) * 100, 2) . "%\n";
    
} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
}

echo "\n✅ انتهت الاختبارات\n";
?>

