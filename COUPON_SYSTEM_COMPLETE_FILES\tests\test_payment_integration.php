<?php
/**
 * اختبارات تكامل الكوبونات مع نظام الدفع
 * Payment Integration Tests
 */

require_once '../config.php';
require_once '../includes/CouponManager.php';
require_once '../includes/CouponValidator.php';
require_once '../includes/CouponPaymentIntegration.php';

class PaymentIntegrationTest {
    private $pdo;
    private $couponManager;
    private $couponValidator;
    private $paymentIntegration;
    private $testResults = [];
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->couponManager = new CouponManager($pdo);
        $this->couponValidator = new CouponValidator($pdo, $this->couponManager);
        $this->paymentIntegration = new CouponPaymentIntegration($pdo, $this->couponManager, $this->couponValidator);
    }
    
    public function runAllTests() {
        echo "🧪 بدء اختبارات تكامل الدفع\n";
        echo "🧪 Starting Payment Integration Tests\n\n";
        
        $this->testProcessPaymentWithCoupon();
        $this->testApplyToInvoice();
        $this->testApplyToPayment();
        $this->testRemoveCoupon();
        $this->testValidateCoupon();
        $this->testCalculateDiscount();
        
        $this->printResults();
    }
    
    private function testProcessPaymentWithCoupon() {
        echo "1️⃣ اختبار معالجة الدفع مع الكوبون...\n";
        try {
            // إنشاء كوبون اختبار
            $coupon = $this->couponManager->createCoupon([
                'code' => 'TEST' . time(),
                'name' => 'Test Coupon',
                'type' => 'percentage',
                'value' => 10,
                'valid_from' => date('Y-m-d H:i:s'),
                'valid_until' => date('Y-m-d H:i:s', strtotime('+30 days'))
            ]);
            
            if ($coupon['success']) {
                $result = $this->paymentIntegration->processPaymentWithCoupon([
                    'coupon_code' => $coupon['coupon']['code'],
                    'amount' => 1000,
                    'user_id' => 1,
                    'plan_id' => 1
                ]);
                
                $this->testResults[] = [
                    'test' => 'معالجة الدفع مع الكوبون',
                    'status' => $result['success'] ? '✅ نجح' : '❌ فشل',
                    'details' => $result['success'] ? 'تم حساب الخصم بنجاح' : $result['error']
                ];
            }
        } catch (Exception $e) {
            $this->testResults[] = [
                'test' => 'معالجة الدفع مع الكوبون',
                'status' => '❌ خطأ',
                'details' => $e->getMessage()
            ];
        }
    }
    
    private function testApplyToInvoice() {
        echo "2️⃣ اختبار تطبيق الكوبون على الفاتورة...\n";
        try {
            // الحصول على فاتورة
            $invoice = $this->pdo->query("SELECT id FROM invoices LIMIT 1")->fetch(PDO::FETCH_ASSOC);
            
            if ($invoice) {
                $coupon = $this->couponManager->createCoupon([
                    'code' => 'INV' . time(),
                    'name' => 'Invoice Test',
                    'type' => 'fixed',
                    'value' => 50,
                    'valid_from' => date('Y-m-d H:i:s'),
                    'valid_until' => date('Y-m-d H:i:s', strtotime('+30 days'))
                ]);
                
                if ($coupon['success']) {
                    $result = $this->paymentIntegration->applyToInvoice(
                        $invoice['id'],
                        $coupon['coupon']['code'],
                        1
                    );
                    
                    $this->testResults[] = [
                        'test' => 'تطبيق الكوبون على الفاتورة',
                        'status' => $result['success'] ? '✅ نجح' : '❌ فشل',
                        'details' => $result['success'] ? 'تم التطبيق بنجاح' : $result['error']
                    ];
                }
            }
        } catch (Exception $e) {
            $this->testResults[] = [
                'test' => 'تطبيق الكوبون على الفاتورة',
                'status' => '❌ خطأ',
                'details' => $e->getMessage()
            ];
        }
    }
    
    private function testApplyToPayment() {
        echo "3️⃣ اختبار تطبيق الكوبون على الدفعة...\n";
        try {
            $payment = $this->pdo->query("SELECT id FROM payments LIMIT 1")->fetch(PDO::FETCH_ASSOC);
            
            if ($payment) {
                $coupon = $this->couponManager->createCoupon([
                    'code' => 'PAY' . time(),
                    'name' => 'Payment Test',
                    'type' => 'percentage',
                    'value' => 5,
                    'valid_from' => date('Y-m-d H:i:s'),
                    'valid_until' => date('Y-m-d H:i:s', strtotime('+30 days'))
                ]);
                
                if ($coupon['success']) {
                    $result = $this->paymentIntegration->applyToPayment(
                        $payment['id'],
                        $coupon['coupon']['code'],
                        1
                    );
                    
                    $this->testResults[] = [
                        'test' => 'تطبيق الكوبون على الدفعة',
                        'status' => $result['success'] ? '✅ نجح' : '❌ فشل',
                        'details' => $result['success'] ? 'تم التطبيق بنجاح' : $result['error']
                    ];
                }
            }
        } catch (Exception $e) {
            $this->testResults[] = [
                'test' => 'تطبيق الكوبون على الدفعة',
                'status' => '❌ خطأ',
                'details' => $e->getMessage()
            ];
        }
    }
    
    private function testRemoveCoupon() {
        echo "4️⃣ اختبار إلغاء الكوبون...\n";
        try {
            $payment = $this->pdo->query("SELECT id FROM payments WHERE coupon_id IS NOT NULL LIMIT 1")->fetch(PDO::FETCH_ASSOC);
            
            if ($payment) {
                $result = $this->paymentIntegration->removeCouponFromPayment($payment['id']);
                $this->testResults[] = [
                    'test' => 'إلغاء الكوبون',
                    'status' => $result['success'] ? '✅ نجح' : '❌ فشل',
                    'details' => $result['success'] ? 'تم الإلغاء بنجاح' : $result['error']
                ];
            }
        } catch (Exception $e) {
            $this->testResults[] = [
                'test' => 'إلغاء الكوبون',
                'status' => '❌ خطأ',
                'details' => $e->getMessage()
            ];
        }
    }
    
    private function testValidateCoupon() {
        echo "5️⃣ اختبار التحقق من صحة الكوبون...\n";
        try {
            $coupon = $this->couponManager->createCoupon([
                'code' => 'VAL' . time(),
                'name' => 'Validation Test',
                'type' => 'percentage',
                'value' => 15,
                'valid_from' => date('Y-m-d H:i:s'),
                'valid_until' => date('Y-m-d H:i:s', strtotime('+30 days'))
            ]);
            
            if ($coupon['success']) {
                $result = $this->couponValidator->validate($coupon['coupon']['code'], [
                    'amount' => 500,
                    'user_id' => 1
                ]);
                
                $this->testResults[] = [
                    'test' => 'التحقق من صحة الكوبون',
                    'status' => $result['valid'] ? '✅ نجح' : '❌ فشل',
                    'details' => $result['valid'] ? 'الكوبون صحيح' : $result['error']
                ];
            }
        } catch (Exception $e) {
            $this->testResults[] = [
                'test' => 'التحقق من صحة الكوبون',
                'status' => '❌ خطأ',
                'details' => $e->getMessage()
            ];
        }
    }
    
    private function testCalculateDiscount() {
        echo "6️⃣ اختبار حساب الخصم...\n";
        try {
            $coupon = $this->couponManager->createCoupon([
                'code' => 'CALC' . time(),
                'name' => 'Calculation Test',
                'type' => 'percentage',
                'value' => 20,
                'valid_from' => date('Y-m-d H:i:s'),
                'valid_until' => date('Y-m-d H:i:s', strtotime('+30 days'))
            ]);
            
            if ($coupon['success']) {
                $discount = $this->couponManager->calculateDiscount($coupon['coupon'], 1000);
                $this->testResults[] = [
                    'test' => 'حساب الخصم',
                    'status' => $discount > 0 ? '✅ نجح' : '❌ فشل',
                    'details' => "الخصم المحسوب: $discount"
                ];
            }
        } catch (Exception $e) {
            $this->testResults[] = [
                'test' => 'حساب الخصم',
                'status' => '❌ خطأ',
                'details' => $e->getMessage()
            ];
        }
    }
    
    private function printResults() {
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "📊 نتائج الاختبارات\n";
        echo "📊 Test Results\n";
        echo str_repeat("=", 60) . "\n\n";
        
        foreach ($this->testResults as $result) {
            echo $result['status'] . " " . $result['test'] . "\n";
            echo "   " . $result['details'] . "\n\n";
        }
        
        $passed = count(array_filter($this->testResults, fn($r) => strpos($r['status'], '✅') !== false));
        $total = count($this->testResults);
        
        echo str_repeat("=", 60) . "\n";
        echo "النتيجة النهائية: $passed/$total اختبارات نجحت\n";
        echo "Final Result: $passed/$total tests passed\n";
    }
}

// تشغيل الاختبارات
$test = new PaymentIntegrationTest($pdo);
$test->runAllTests();
?>

