-- =====================================================
-- إعداد فائق الأمان لنظام الكوبونات
-- Ultra Safe Setup for Coupon System
-- =====================================================

-- حذف الجداول إذا كانت موجودة
DROP TABLE IF EXISTS coupon_usage;
DROP TABLE IF EXISTS coupons;

-- إن<PERSON><PERSON><PERSON> جدول الكوبونات الأساسي
CREATE TABLE coupons (
  id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  uuid VARCHAR(50) NOT NULL DEFAULT '',
  code VARCHAR(50) UNIQUE NOT NULL,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  type VARCHAR(20) NOT NULL DEFAULT 'percentage',
  value DECIMAL(10,2) NOT NULL DEFAULT 0,
  minimum_amount DECIMAL(10,2) DEFAULT 0,
  maximum_discount DECIMAL(10,2) DEFAULT NULL,
  usage_limit INT UNSIGNED DEFAULT NULL,
  usage_limit_per_user TINYINT UNSIGNED DEFAULT 1,
  used_count INT UNSIGNED DEFAULT 0,
  valid_from DATETIME NOT NULL,
  valid_until DATETIME NOT NULL,
  is_active TINYINT(1) DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- إنشاء جدول استخدام الكوبونات الأساسي
CREATE TABLE coupon_usage (
  id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  coupon_id BIGINT UNSIGNED NOT NULL,
  user_id BIGINT UNSIGNED NOT NULL,
  discount_amount DECIMAL(10,2) NOT NULL,
  original_amount DECIMAL(10,2) NOT NULL,
  final_amount DECIMAL(10,2) NOT NULL,
  status VARCHAR(20) DEFAULT 'applied',
  used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- إدراج كوبون تجريبي واحد للاختبار
INSERT INTO coupons (
    uuid,
    code,
    name,
    description,
    type,
    value,
    minimum_amount,
    valid_from,
    valid_until,
    is_active
) VALUES (
    'test-uuid-001',
    'TEST2025',
    'كوبون اختبار 2025',
    'كوبون تجريبي للاختبار',
    'percentage',
    10.00,
    50.00,
    '2025-01-01 00:00:00',
    '2025-12-31 23:59:59',
    1
);

-- إدراج كوبون ثاني
INSERT INTO coupons (
    uuid,
    code,
    name,
    description,
    type,
    value,
    minimum_amount,
    valid_from,
    valid_until,
    is_active
) VALUES (
    'fixed-uuid-002',
    'FIXED50',
    'خصم ثابت 50',
    'كوبون خصم ثابت 50 ريال',
    'fixed',
    50.00,
    100.00,
    '2025-01-01 00:00:00',
    '2025-12-31 23:59:59',
    1
);

-- إدراج كوبون ثالث
INSERT INTO coupons (
    uuid,
    code,
    name,
    description,
    type,
    value,
    minimum_amount,
    valid_from,
    valid_until,
    is_active
) VALUES (
    'simple-uuid-003',
    'SIMPLE2025',
    'كوبون بسيط 2025',
    'كوبون اختبار بسيط',
    'percentage',
    15.00,
    30.00,
    '2025-01-01 00:00:00',
    '2025-12-31 23:59:59',
    1
);

-- فحص النتائج
SELECT 'تم إنشاء الجداول وإدراج الكوبونات بنجاح!' AS message;
SELECT COUNT(*) AS total_coupons FROM coupons;
SELECT id, code, name, type, value, is_active FROM coupons WHERE is_active = 1;
