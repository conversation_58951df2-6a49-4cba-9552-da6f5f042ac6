<?php
/**
 * إنشاء كوبون جديد - Create New Coupon
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/layout.php';
require_once __DIR__ . '/../includes/CouponManager.php';

require_roles(['admin', 'super_admin']);

$pdo = getDBConnection();
$couponManager = new CouponManager($pdo);
$msg = '';
$error = '';

// معالجة الإرسال
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $code = trim($_POST['code'] ?? '');
        $type = $_POST['type'] ?? 'percent';
        $value = (float)($_POST['value'] ?? 0);
        $description = trim($_POST['description'] ?? '');
        $min_amount = ($_POST['min_amount'] ?? '') ? (float)$_POST['min_amount'] : null;
        $max_discount = ($_POST['max_discount'] ?? '') ? (float)$_POST['max_discount'] : null;
        $usage_limit = ($_POST['usage_limit'] ?? '') ? (int)$_POST['usage_limit'] : null;
        $expires_at = $_POST['expires_at'] ?? null;
        $applicable_to = $_POST['applicable_to'] ?? 'all';
        
        if (!$code || $value <= 0) {
            throw new Exception('يرجى إدخال كود صحيح وقيمة خصم أكبر من صفر');
        }
        
        $couponData = [
            'code' => $code,
            'type' => $type,
            'value' => $value,
            'description' => $description,
            'min_amount' => $min_amount,
            'max_discount' => $max_discount,
            'usage_limit' => $usage_limit,
            'expires_at' => $expires_at,
            'applicable_to' => $applicable_to,
            'is_active' => 1
        ];
        
        $result = $couponManager->createCoupon($couponData);
        
        if ($result['success']) {
            $msg = 'تم إنشاء الكوبون بنجاح!';
            $_POST = [];
        } else {
            $error = $result['error'] ?? 'حدث خطأ أثناء الإنشاء';
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// الحصول على الخطط المتاحة
$plans = [];
try {
    $stmt = $pdo->query("SELECT id, name FROM subscription_plans WHERE is_active = 1");
    $plans = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء كوبون جديد</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f7fa; }
        .container { max-width: 800px; margin: 0 auto; padding: 20px; }
        .header { margin-bottom: 30px; }
        .header h1 { color: #2c3e50; font-size: 28px; margin-bottom: 10px; }
        
        .form-card {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            font-size: 14px;
            font-family: inherit;
        }
        
        .form-group textarea { resize: vertical; min-height: 80px; }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .alert {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 30px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .btn-primary { background: #3498db; color: white; flex: 1; }
        .btn-primary:hover { background: #2980b9; }
        .btn-secondary { background: #95a5a6; color: white; flex: 1; }
        .btn-secondary:hover { background: #7f8c8d; }
        
        .help-text {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-plus-circle"></i> إنشاء كوبون جديد</h1>
        </div>
        
        <?php if ($msg): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo $msg; ?>
            </div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
            </div>
        <?php endif; ?>
        
        <div class="form-card">
            <form method="POST">
                <!-- معلومات الكوبون الأساسية -->
                <h3 style="margin-bottom: 20px; color: #2c3e50;">
                    <i class="fas fa-info-circle"></i> معلومات الكوبون
                </h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="code">كود الكوبون *</label>
                        <input type="text" id="code" name="code" required placeholder="مثال: SAVE10">
                        <div class="help-text">كود فريد يستخدمه العملاء</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="type">نوع الخصم *</label>
                        <select id="type" name="type" required>
                            <option value="percent">نسبة مئوية (%)</option>
                            <option value="fixed">مبلغ ثابت (ريال)</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="value">قيمة الخصم *</label>
                        <input type="number" id="value" name="value" required step="0.01" placeholder="10">
                        <div class="help-text">قيمة الخصم (نسبة أو مبلغ)</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="description">الوصف</label>
                        <input type="text" id="description" name="description" placeholder="وصف الكوبون">
                    </div>
                </div>
                
                <!-- شروط الاستخدام -->
                <h3 style="margin-top: 30px; margin-bottom: 20px; color: #2c3e50;">
                    <i class="fas fa-rules"></i> شروط الاستخدام
                </h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="min_amount">الحد الأدنى للمبلغ</label>
                        <input type="number" id="min_amount" name="min_amount" step="0.01" placeholder="0">
                        <div class="help-text">الحد الأدنى للمبلغ لتطبيق الكوبون</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="max_discount">الحد الأقصى للخصم</label>
                        <input type="number" id="max_discount" name="max_discount" step="0.01" placeholder="0">
                        <div class="help-text">الحد الأقصى للخصم المسموح</div>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="usage_limit">حد الاستخدام</label>
                        <input type="number" id="usage_limit" name="usage_limit" placeholder="بدون حد">
                        <div class="help-text">عدد مرات الاستخدام المسموحة</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="expires_at">تاريخ الانتهاء</label>
                        <input type="datetime-local" id="expires_at" name="expires_at">
                        <div class="help-text">تاريخ انتهاء صلاحية الكوبون</div>
                    </div>
                </div>
                
                <!-- التطبيق على -->
                <h3 style="margin-top: 30px; margin-bottom: 20px; color: #2c3e50;">
                    <i class="fas fa-target"></i> التطبيق على
                </h3>
                
                <div class="form-group">
                    <label for="applicable_to">ينطبق على *</label>
                    <select id="applicable_to" name="applicable_to" required>
                        <option value="all">جميع الخطط</option>
                        <option value="specific">خطط محددة</option>
                        <option value="subscriptions">الاشتراكات فقط</option>
                        <option value="payments">الدفعات فقط</option>
                    </select>
                </div>
                
                <!-- الأزرار -->
                <div class="btn-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> إنشاء الكوبون
                    </button>
                    <a href="coupon_dashboard.php" class="btn btn-secondary" style="text-decoration: none; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-arrow-left"></i> العودة
                    </a>
                </div>
            </form>
        </div>
    </div>
</body>
</html>

