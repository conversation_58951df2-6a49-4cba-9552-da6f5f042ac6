-- =====================================================
-- إعداد آمن ومبسط لنظام الكوبونات
-- Simple Safe Setup for Coupon System
-- =====================================================

-- حذ<PERSON> الجداول إذا كانت موجودة
DROP TABLE IF EXISTS `coupon_usage`;
DROP TABLE IF EXISTS `coupons`;

-- إن<PERSON>اء جدول الكوبونات المبسط
CREATE TABLE `coupons` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `uuid` CHAR(36) NOT NULL DEFAULT '',
  `code` VARCHAR(50) UNIQUE NOT NULL,
  `name` VARCHAR(100) NOT NULL,
  `description` TEXT NULL,
  `type` ENUM('percentage','fixed') NOT NULL DEFAULT 'percentage',
  `value` DECIMAL(10,2) NOT NULL DEFAULT 0,
  `minimum_amount` DECIMAL(10,2) DEFAULT 0,
  `maximum_discount` DECIMAL(10,2) NULL,
  `usage_limit` INT UNSIGNED NULL,
  `usage_limit_per_user` TINYINT UNSIGNED DEFAULT 1,
  `used_count` INT UNSIGNED DEFAULT 0,
  `valid_from` DATETIME NOT NULL,
  `valid_until` DATETIME NOT NULL,
  `is_active` TINYINT(1) DEFAULT 1,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_code` (`code`),
  INDEX `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول استخدام الكوبونات المبسط
CREATE TABLE `coupon_usage` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `coupon_id` BIGINT UNSIGNED NOT NULL,
  `user_id` BIGINT UNSIGNED NOT NULL,
  `discount_amount` DECIMAL(10,2) NOT NULL,
  `original_amount` DECIMAL(10,2) NOT NULL,
  `final_amount` DECIMAL(10,2) NOT NULL,
  `status` ENUM('applied','cancelled','refunded') DEFAULT 'applied',
  `used_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX `idx_coupon_id` (`coupon_id`),
  INDEX `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج كوبونات تجريبية بطريقة آمنة
INSERT INTO `coupons` (
    `uuid`,
    `code`,
    `name`,
    `description`,
    `type`,
    `value`,
    `minimum_amount`,
    `maximum_discount`,
    `usage_limit`,
    `usage_limit_per_user`,
    `used_count`,
    `valid_from`,
    `valid_until`,
    `is_active`
) VALUES 
('test-2025-uuid-001', 'TEST2025', 'كوبون اختبار 2025', 'كوبون تجريبي للاختبار', 'percentage', 10.00, 50.00, 100.00, 100, 1, 0, '2025-01-01 00:00:00', '2025-12-31 23:59:59', 1),
('fixed-50-uuid-002', 'FIXED50', 'خصم ثابت 50', 'كوبون خصم ثابت 50 ريال', 'fixed', 50.00, 100.00, NULL, 50, 1, 0, '2025-01-01 00:00:00', '2025-12-31 23:59:59', 1),
('simple-2025-uuid-003', 'SIMPLE2025', 'كوبون بسيط 2025', 'كوبون اختبار بسيط', 'percentage', 15.00, 30.00, 200.00, 200, 1, 0, '2025-01-01 00:00:00', '2025-12-31 23:59:59', 1);

-- إدراج كوبون إضافي للاختبار
INSERT INTO `coupons` (
    `uuid`,
    `code`,
    `name`,
    `description`,
    `type`,
    `value`,
    `minimum_amount`,
    `valid_from`,
    `valid_until`,
    `is_active`
) VALUES 
('safe-test-uuid-004', 'SAFE2025', 'كوبون آمن للاختبار', 'كوبون مبسط للاختبار الآمن', 'percentage', 25.00, 20.00, '2025-01-01 00:00:00', '2025-12-31 23:59:59', 1);

-- فحص النتائج
SELECT 'تم إنشاء الجداول وإدراج الكوبونات بنجاح!' AS message;
SELECT COUNT(*) AS total_coupons FROM coupons;
SELECT id, code, name, type, value, is_active FROM coupons WHERE is_active = 1;
