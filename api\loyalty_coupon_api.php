<?php
/**
 * API للتكامل بين الكوبونات والولاء
 * Loyalty Coupon Integration API
 */

header('Content-Type: application/json; charset=utf-8');

session_start();
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/CouponManager.php';
require_once __DIR__ . '/../includes/CouponLoyaltyIntegration.php';

// التحقق من المصادقة
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'غير مصرح']);
    exit;
}

$couponManager = new CouponManager($pdo);
$loyaltyIntegration = new CouponLoyaltyIntegration($pdo, $couponManager);

$action = $_GET['action'] ?? $_POST['action'] ?? '';
$userId = $_SESSION['user_id'];

try {
    switch ($action) {
        // 1. الحصول على نقاط الولاء للمستخدم
        case 'get_user_points':
            $stats = $loyaltyIntegration->getUserLoyaltyStats($userId);
            echo json_encode($stats);
            break;
        
        // 2. الحصول على الكوبونات المتاحة للاستبدال
        case 'get_available_coupons':
            $limit = (int)($_GET['limit'] ?? 10);
            $result = $loyaltyIntegration->getAvailableLoyaltyCoupons($userId, $limit);
            echo json_encode($result);
            break;
        
        // 3. استبدال كوبون بنقاط الولاء
        case 'redeem_coupon':
            $couponId = (int)($_POST['coupon_id'] ?? 0);
            if (!$couponId) {
                throw new Exception('معرف الكوبون مطلوب');
            }
            
            $result = $loyaltyIntegration->redeemCouponWithLoyaltyPoints($couponId, $userId);
            echo json_encode($result);
            break;
        
        // 4. تطبيق كوبون الولاء على عملية شراء
        case 'apply_loyalty_coupon':
            $couponId = (int)($_POST['coupon_id'] ?? 0);
            $purchaseAmount = (float)($_POST['purchase_amount'] ?? 0);
            
            if (!$couponId || $purchaseAmount <= 0) {
                throw new Exception('معرف الكوبون والمبلغ مطلوبان');
            }
            
            $result = $loyaltyIntegration->applyLoyaltyCouponToPurchase($couponId, $userId, $purchaseAmount);
            echo json_encode($result);
            break;
        
        // 5. الحصول على سجل المعاملات
        case 'get_transaction_history':
            $limit = (int)($_GET['limit'] ?? 50);
            $offset = (int)($_GET['offset'] ?? 0);
            
            $result = $loyaltyIntegration->getUserLoyaltyHistory($userId, $limit, $offset);
            echo json_encode($result);
            break;
        
        // 6. حساب نقاط الولاء من قيمة الخصم
        case 'calculate_points':
            $discountAmount = (float)($_POST['discount_amount'] ?? 0);
            $couponId = (int)($_POST['coupon_id'] ?? null);
            
            if ($discountAmount <= 0) {
                throw new Exception('قيمة الخصم مطلوبة');
            }
            
            $points = $loyaltyIntegration->calculateLoyaltyPointsFromDiscount($discountAmount, $couponId);
            echo json_encode([
                'success' => true,
                'discount_amount' => $discountAmount,
                'loyalty_points' => $points
            ]);
            break;
        
        // 7. منح نقاط الولاء يدويًا (للمسؤولين فقط)
        case 'award_points':
            if ($_SESSION['role'] !== 'admin') {
                throw new Exception('غير مصرح');
            }
            
            $targetUserId = (int)($_POST['user_id'] ?? 0);
            $points = (int)($_POST['points'] ?? 0);
            $reason = trim($_POST['reason'] ?? '');
            
            if (!$targetUserId || $points <= 0) {
                throw new Exception('معرف المستخدم والنقاط مطلوبان');
            }
            
            // إضافة النقاط
            $sql = "INSERT INTO loyalty_points_ledger (player_id, source, delta, description) 
                    VALUES (?, 'admin_award', ?, ?)";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$targetUserId, $points, $reason]);
            
            echo json_encode([
                'success' => true,
                'message' => "تم منح {$points} نقطة للمستخدم",
                'points_awarded' => $points
            ]);
            break;
        
        // 8. الحصول على إحصائيات الولاء (للمسؤولين)
        case 'get_loyalty_stats':
            if ($_SESSION['role'] !== 'admin') {
                throw new Exception('غير مصرح');
            }
            
            $stats = [
                'total_users_with_points' => $pdo->query("SELECT COUNT(DISTINCT player_id) FROM loyalty_points_ledger")->fetchColumn(),
                'total_points_awarded' => $pdo->query("SELECT COALESCE(SUM(delta), 0) FROM loyalty_points_ledger WHERE delta > 0")->fetchColumn(),
                'total_points_redeemed' => $pdo->query("SELECT COALESCE(SUM(ABS(delta)), 0) FROM loyalty_points_ledger WHERE delta < 0")->fetchColumn(),
                'average_points_per_user' => $pdo->query("SELECT COALESCE(AVG(points_balance), 0) FROM loyalty_points")->fetchColumn(),
                'top_earners' => $pdo->query("SELECT player_id, SUM(delta) as total_points FROM loyalty_points_ledger WHERE delta > 0 GROUP BY player_id ORDER BY total_points DESC LIMIT 10")->fetchAll(PDO::FETCH_ASSOC)
            ];
            
            echo json_encode([
                'success' => true,
                'stats' => $stats
            ]);
            break;
        
        default:
            throw new Exception('إجراء غير معروف: ' . $action);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>

