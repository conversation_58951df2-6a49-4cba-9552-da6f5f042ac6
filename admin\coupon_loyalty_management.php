<?php
/**
 * إدارة الكوبونات المرتبطة بالولاء
 * Loyalty Coupon Management
 */

session_start();
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/CouponManager.php';
require_once __DIR__ . '/../includes/CouponLoyaltyIntegration.php';

// التحقق من الصلاحيات
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    http_response_code(403);
    exit('Access Denied');
}

$couponManager = new CouponManager($pdo);
$loyaltyIntegration = new CouponLoyaltyIntegration($pdo, $couponManager);

$action = $_GET['action'] ?? 'list';
$message = '';
$error = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? 'list';
    
    if ($action === 'create_loyalty_coupon') {
        try {
            $couponData = [
                'code' => trim($_POST['code']),
                'name' => trim($_POST['name']),
                'type' => $_POST['type'],
                'value' => (float)$_POST['value'],
                'max_discount' => (float)($_POST['max_discount'] ?? 0),
                'min_amount' => (float)($_POST['min_amount'] ?? 0),
                'usage_limit' => (int)($_POST['usage_limit'] ?? 0),
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'loyalty_multiplier' => (float)($_POST['loyalty_multiplier'] ?? 1.00),
                'loyalty_enabled' => 1
            ];
            
            $loyaltyData = [
                'points_required' => (int)($_POST['points_required'] ?? 0),
                'points_earned' => (int)($_POST['points_earned'] ?? 0)
            ];
            
            $result = $loyaltyIntegration->createLoyaltyCoupon($couponData, $loyaltyData);
            
            if ($result['success']) {
                $message = "✅ تم إنشاء كوبون الولاء بنجاح (ID: {$result['coupon_id']})";
            } else {
                $error = "❌ خطأ: " . $result['error'];
            }
        } catch (Exception $e) {
            $error = "❌ خطأ: " . $e->getMessage();
        }
    }
    
    if ($action === 'update_loyalty_mapping') {
        try {
            $couponId = (int)$_POST['coupon_id'];
            $pointsRequired = (int)$_POST['points_required'];
            $pointsEarned = (int)$_POST['points_earned'];
            $multiplier = (float)$_POST['multiplier'];
            
            $sql = "INSERT INTO coupon_loyalty_mapping (coupon_id, loyalty_points_required, loyalty_points_earned, loyalty_multiplier)
                    VALUES (?, ?, ?, ?)
                    ON DUPLICATE KEY UPDATE
                    loyalty_points_required = ?, loyalty_points_earned = ?, loyalty_multiplier = ?";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$couponId, $pointsRequired, $pointsEarned, $multiplier, $pointsRequired, $pointsEarned, $multiplier]);
            
            $message = "✅ تم تحديث ربط الولاء بنجاح";
        } catch (Exception $e) {
            $error = "❌ خطأ: " . $e->getMessage();
        }
    }
}

// جلب الكوبونات المرتبطة بالولاء
$loyaltyCoupons = [];
try {
    $sql = "SELECT c.*, clm.loyalty_points_required, clm.loyalty_points_earned, clm.loyalty_multiplier
            FROM coupons c
            LEFT JOIN coupon_loyalty_mapping clm ON c.id = clm.coupon_id
            WHERE c.loyalty_enabled = 1
            ORDER BY c.created_at DESC";
    
    $stmt = $pdo->query($sql);
    $loyaltyCoupons = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $error = "خطأ في جلب البيانات: " . $e->getMessage();
}

// جلب إحصائيات الولاء
$stats = [];
try {
    $stats = [
        'total_loyalty_coupons' => count($loyaltyCoupons),
        'active_coupons' => $pdo->query("SELECT COUNT(*) FROM coupons WHERE loyalty_enabled = 1 AND is_active = 1")->fetchColumn(),
        'total_points_awarded' => $pdo->query("SELECT COALESCE(SUM(delta), 0) FROM loyalty_points_ledger WHERE source = 'coupon_usage'")->fetchColumn(),
        'total_points_redeemed' => $pdo->query("SELECT COALESCE(SUM(ABS(delta)), 0) FROM loyalty_points_ledger WHERE source = 'coupon_redemption'")->fetchColumn()
    ];
} catch (Exception $e) {
    // تجاهل الأخطاء في الإحصائيات
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة كوبونات الولاء</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .card { border: none; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .stat-card.success { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .stat-card.info { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .stat-card.warning { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
        .table-hover tbody tr:hover { background-color: #f5f5f5; }
        .badge-loyalty { background-color: #667eea; }
    </style>
</head>
<body>
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="mb-4"><i class="fas fa-gift"></i> إدارة كوبونات الولاء</h1>
        </div>
    </div>
    
    <!-- الرسائل -->
    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stat-card">
                <div class="card-body">
                    <h6 class="card-title">إجمالي كوبونات الولاء</h6>
                    <h2><?php echo $stats['total_loyalty_coupons'] ?? 0; ?></h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card success">
                <div class="card-body">
                    <h6 class="card-title">الكوبونات النشطة</h6>
                    <h2><?php echo $stats['active_coupons'] ?? 0; ?></h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card info">
                <div class="card-body">
                    <h6 class="card-title">النقاط الممنوحة</h6>
                    <h2><?php echo number_format($stats['total_points_awarded'] ?? 0); ?></h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stat-card warning">
                <div class="card-body">
                    <h6 class="card-title">النقاط المستخدمة</h6>
                    <h2><?php echo number_format($stats['total_points_redeemed'] ?? 0); ?></h2>
                </div>
            </div>
        </div>
    </div>
    
    <!-- زر إنشاء كوبون جديد -->
    <div class="row mb-4">
        <div class="col-12">
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createCouponModal">
                <i class="fas fa-plus"></i> إنشاء كوبون ولاء جديد
            </button>
        </div>
    </div>
    
    <!-- جدول الكوبونات -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-list"></i> قائمة كوبونات الولاء</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>الكود</th>
                                    <th>الاسم</th>
                                    <th>النوع</th>
                                    <th>القيمة</th>
                                    <th>النقاط المطلوبة</th>
                                    <th>النقاط المكتسبة</th>
                                    <th>المضاعف</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($loyaltyCoupons as $coupon): ?>
                                <tr>
                                    <td><span class="badge badge-loyalty"><?php echo htmlspecialchars($coupon['code']); ?></span></td>
                                    <td><?php echo htmlspecialchars($coupon['name']); ?></td>
                                    <td><?php echo $coupon['type'] === 'fixed' ? 'مبلغ ثابت' : 'نسبة مئوية'; ?></td>
                                    <td><?php echo number_format($coupon['value'], 2); ?></td>
                                    <td><?php echo $coupon['loyalty_points_required'] ?? '-'; ?></td>
                                    <td><?php echo $coupon['loyalty_points_earned'] ?? '-'; ?></td>
                                    <td><?php echo number_format($coupon['loyalty_multiplier'] ?? 1.00, 2); ?>x</td>
                                    <td>
                                        <?php if ($coupon['is_active']): ?>
                                            <span class="badge bg-success">نشط</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">معطل</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#editCouponModal" 
                                                onclick="editCoupon(<?php echo $coupon['id']; ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal: إنشاء كوبون جديد -->
<div class="modal fade" id="createCouponModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إنشاء كوبون ولاء جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create_loyalty_coupon">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الكود</label>
                            <input type="text" class="form-control" name="code" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الاسم</label>
                            <input type="text" class="form-control" name="name" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">النوع</label>
                            <select class="form-control" name="type" required>
                                <option value="fixed">مبلغ ثابت</option>
                                <option value="percentage">نسبة مئوية</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">القيمة</label>
                            <input type="number" class="form-control" name="value" step="0.01" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">النقاط المطلوبة للاستبدال</label>
                            <input type="number" class="form-control" name="points_required" value="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">النقاط المكتسبة عند الاستخدام</label>
                            <input type="number" class="form-control" name="points_earned" value="0">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">مضاعف النقاط</label>
                            <input type="number" class="form-control" name="loyalty_multiplier" step="0.01" value="1.00">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">حد أقصى للخصم</label>
                            <input type="number" class="form-control" name="max_discount" step="0.01" value="0">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" name="is_active" id="isActive" checked>
                            <label class="form-check-label" for="isActive">تفعيل الكوبون</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إنشاء</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

