<?php
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/layout.php';
require_once __DIR__ . '/../includes/CouponManager.php';
require_once __DIR__ . '/../includes/CouponValidator.php';
require_once __DIR__ . '/../includes/CouponReportingSystem.php';

// ============ حراسة المصادقة ============
require_roles(['admin', 'super_admin']);
// ============ نهاية حراسة المصادقة ============

$pdo = getDBConnection();
$couponManager = new CouponManager($pdo);
$couponValidator = new CouponValidator($pdo, $couponManager);
$reportingSystem = new CouponReportingSystem($pdo);

function ensure_coupons(PDO $pdo){
  $pdo->exec("CREATE TABLE IF NOT EXISTS coupons (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(64) UNIQUE NOT NULL,
    type ENUM('percent','fixed') NOT NULL DEFAULT 'percent',
    value DECIMAL(10,2) NOT NULL,
    active TINYINT(1) DEFAULT 1,
    expires_at DATETIME NULL,
    usage_limit INT NULL,
    used_count INT DEFAULT 0,
    min_amount DECIMAL(10,2) NULL,
    applicable_plans TEXT NULL,
    notes VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
}

// Migration helpers to avoid Unknown column errors
function column_exists(PDO $pdo, string $table, string $column): bool {
  // بعض إصدارات MariaDB لا تدعم placeholders في أوامر SHOW
  $t = str_replace('`','``',$table);
  $c = str_replace('`','``',$column);
  $sql = "SHOW COLUMNS FROM `{$t}` LIKE " . $pdo->quote($c);
  $rows = $pdo->query($sql)->fetchAll(PDO::FETCH_ASSOC);
  return !empty($rows);
}
function ensure_coupons_migration(PDO $pdo){
  // Add missing columns gracefully
  if (!column_exists($pdo,'coupons','min_amount')) {
    $pdo->exec("ALTER TABLE coupons ADD COLUMN min_amount DECIMAL(10,2) NULL AFTER used_count");
  }
  if (!column_exists($pdo,'coupons','applicable_plans')) {
    $pdo->exec("ALTER TABLE coupons ADD COLUMN applicable_plans TEXT NULL AFTER min_amount");
  }
  if (!column_exists($pdo,'coupons','notes')) {
    $pdo->exec("ALTER TABLE coupons ADD COLUMN notes VARCHAR(255) NULL AFTER applicable_plans");
  }
  // Optional newer fields (won't be used by this UI but future-proof)
  if (!column_exists($pdo,'coupons','discount_scope')) {
    try { $pdo->exec("ALTER TABLE coupons ADD COLUMN discount_scope ENUM('final','plan','transport','services') NOT NULL DEFAULT 'final' AFTER notes"); } catch(Throwable $e){}
  }
  if (!column_exists($pdo,'coupons','applicable_services')) {
    try { $pdo->exec("ALTER TABLE coupons ADD COLUMN applicable_services TEXT NULL AFTER discount_scope"); } catch(Throwable $e){}
  }
  if (!column_exists($pdo,'coupons','excluded_services')) {
    try { $pdo->exec("ALTER TABLE coupons ADD COLUMN excluded_services TEXT NULL AFTER applicable_services"); } catch(Throwable $e){}
  }
}
function get_table_columns(PDO $pdo, string $table='coupons'): array {
  $t = str_replace('`','``',$table);
  $rows = $pdo->query("SHOW COLUMNS FROM `{$t}`")->fetchAll(PDO::FETCH_ASSOC);
  return array_map(function($r){ return $r['Field']; }, $rows);
}

ensure_coupons($pdo);
ensure_coupons_migration($pdo);

// Handle actions
$action = $_POST['action'] ?? '';
$msg = '';
try {
  if ($action === 'create' || $action === 'update') {
    $id = (int)($_POST['id'] ?? 0);
    $code = trim($_POST['code'] ?? '');
    $type = in_array(($_POST['type'] ?? 'percent'), ['percent','fixed'], true) ? $_POST['type'] : 'percent';
    $value = (float)($_POST['value'] ?? 0);
    $active = (int)($_POST['active'] ?? 1);
    $expires_at = ($_POST['expires_at'] ?? '') ?: null;
    $usage_limit = ($_POST['usage_limit'] ?? '') !== '' ? (int)$_POST['usage_limit'] : null;
    $min_amount = ($_POST['min_amount'] ?? '') !== '' ? (float)$_POST['min_amount'] : null;
    $applicable_plans = trim($_POST['applicable_plans'] ?? '');
    $notes = trim($_POST['notes'] ?? '');

    if ($code === '' || $value <= 0) throw new Exception('يرجى إدخال كود صحيح وقيمة خصم أكبر من صفر');

    $plansJson = $applicable_plans ? json_encode(array_values(array_filter(array_map('trim', explode(',', $applicable_plans))))) : null;

    // Build payload and filter by actual columns to avoid Unknown column errors
    $cols = get_table_columns($pdo, 'coupons');
    $payload = [
      'code' => $code,
      'type' => $type,
      'value' => $value,
      'active' => $active,
      'expires_at' => $expires_at,
      'usage_limit' => $usage_limit,
      'min_amount' => $min_amount,
      'applicable_plans' => $plansJson,
      'notes' => $notes
    ];
    $fields = [];$values=[];
    foreach($payload as $k=>$v){ if(in_array($k,$cols,true)){ $fields[]=$k; $values[]=$v; } }

    if ($action === 'create') {
      $placeholders = implode(',', array_fill(0, count($fields), '?'));
      $columnsList = '`' . implode('`,`', $fields) . '`';
      $sql = "INSERT INTO coupons ($columnsList) VALUES ($placeholders)";
      $st = $pdo->prepare($sql);
      $st->execute($values);
      $msg = 'تم إضافة الكوبون بنجاح';
    } else {
      if (!$id) throw new Exception('معرّف مفقود');
      $sets=[]; $updateValues=[];
      foreach($fields as $f){ $sets[] = "`$f`=?"; $updateValues[] = $payload[$f]; }
      $sql = 'UPDATE coupons SET ' . implode(',', $sets) . ' WHERE id=?';
      $updateValues[] = $id;
      $st = $pdo->prepare($sql);
      $st->execute($updateValues);
      $msg = 'تم تحديث الكوبون';
    }
  } elseif ($action === 'toggle') {
    $id = (int)($_POST['id'] ?? 0);
    if ($id) { $pdo->prepare('UPDATE coupons SET active = 1 - active WHERE id=?')->execute([$id]); $msg = 'تم التبديل'; }
  } elseif ($action === 'delete') {
    $id = (int)($_POST['id'] ?? 0);
    if ($id) { $pdo->prepare('DELETE FROM coupons WHERE id=?')->execute([$id]); $msg = 'تم الحذف'; }
  }
} catch (Throwable $e) { $msg = 'خطأ: ' . $e->getMessage(); }

// Load coupons
$rows = $pdo->query('SELECT * FROM coupons ORDER BY id DESC')->fetchAll(PDO::FETCH_ASSOC);

render_header('إدارة كوبونات الخصم');
?>
<div class="container">
  <div class="row">
    <div class="col-lg-5">
      <div class="card smart mb-4">
        <div class="card-header"><i class="fa fa-plus me-2"></i>إضافة كوبون</div>
        <div class="card-body">
          <?php if ($msg): ?><div class="alert alert-light"><?php echo htmlspecialchars($msg); ?></div><?php endif; ?>
          <form method="post">
            <input type="hidden" name="action" value="create">
            <div class="mb-2">
              <label class="form-label">كود الكوبون</label>
              <input class="form-control" name="code" placeholder="مثال: WELCOME10" required>
            </div>
            <div class="row g-2">
              <div class="col-md-6">
                <label class="form-label">نوع الخصم</label>
                <select class="form-select" name="type">
                  <option value="percent">نسبة %</option>
                  <option value="fixed">قيمة ثابتة</option>
                </select>
              </div>
              <div class="col-md-6">
                <label class="form-label">القيمة</label>
                <input class="form-control" name="value" type="number" step="0.01" min="0.01" required>
              </div>
            </div>
            <div class="row g-2 mt-1">
              <div class="col-md-6">
                <label class="form-label">الحالة</label>
                <select class="form-select" name="active">
                  <option value="1" selected>مفعل</option>
                  <option value="0">معطل</option>
                </select>
              </div>
              <div class="col-md-6">
                <label class="form-label">ينتهي في</label>
                <input class="form-control" name="expires_at" type="datetime-local">
              </div>
            </div>
            <div class="row g-2 mt-1">
              <div class="col-md-6">
                <label class="form-label">حد الاستخدام</label>
                <input class="form-control" name="usage_limit" type="number" min="0" placeholder="اختياري">
              </div>
              <div class="col-md-6">
                <label class="form-label">حد أدنى للمبلغ</label>
                <input class="form-control" name="min_amount" type="number" min="0" step="0.01" placeholder="اختياري">
              </div>
            </div>
            <div class="mt-2">
              <label class="form-label">الخُطط المسموح بها</label>
              <input class="form-control" name="applicable_plans" placeholder="افصل بينها بفواصل، مثال: خطة شهرية,خطة سنوية">
              <small class="text-light">اتركه فارغاً للسماح بكل الخطط</small>
            </div>
            <div class="mt-2">
              <label class="form-label">ملاحظات</label>
              <input class="form-control" name="notes" placeholder="اختياري">
            </div>
            <div class="mt-3 text-end">
              <button class="btn btn-success"><i class="fa fa-save me-1"></i>حفظ</button>
            </div>
          </form>
        </div>
      </div>
    </div>
    <div class="col-lg-7">
      <div class="card smart mb-4">
        <div class="card-header"><i class="fa fa-tags me-2"></i>الكوبونات</div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-dark table-striped align-middle">
              <thead><tr>
                <th>#</th><th>الكود</th><th>النوع</th><th>القيمة</th><th>الحالة</th><th>ينتهي</th><th>استخدام</th><th>خيارات</th>
              </tr></thead>
              <tbody>
              <?php foreach ($rows as $r): ?>
                <tr>
                  <td><?php echo (int)$r['id']; ?></td>
                  <td class="fw-bold"><?php echo htmlspecialchars($r['code']); ?></td>
                  <td><?php echo $r['type']==='percent'?'% نسبة':'قيمة ثابتة'; ?></td>
                  <td><?php echo (float)$r['value']; ?></td>
                  <td><?php echo ((int)$r['active'])?'<span class="badge bg-success">مفعل</span>':'<span class="badge bg-secondary">معطل</span>'; ?></td>
                  <td><?php echo $r['expires_at']?htmlspecialchars($r['expires_at']):'-'; ?></td>
                  <td><?php echo (int)$r['used_count'] . ($r['usage_limit']? ' / '.(int)$r['usage_limit']:''); ?></td>
                  <td>
                    <form method="post" class="d-inline">
                      <input type="hidden" name="action" value="toggle">
                      <input type="hidden" name="id" value="<?php echo (int)$r['id']; ?>">
                      <button class="btn btn-sm btn-warning"><i class="fa fa-power-off"></i></button>
                    </form>
                    <button class="btn btn-sm btn-info" data-bs-toggle="collapse" data-bs-target="#edit<?php echo (int)$r['id']; ?>"><i class="fa fa-edit"></i></button>
                    <form method="post" class="d-inline" onsubmit="return confirm('حذف الكوبون؟');">
                      <input type="hidden" name="action" value="delete">
                      <input type="hidden" name="id" value="<?php echo (int)$r['id']; ?>">
                      <button class="btn btn-sm btn-danger"><i class="fa fa-trash"></i></button>
                    </form>
                  </td>
                </tr>
                <tr class="collapse" id="edit<?php echo (int)$r['id']; ?>">
                  <td colspan="8">
                    <form method="post" class="row g-2">
                      <input type="hidden" name="action" value="update">
                      <input type="hidden" name="id" value="<?php echo (int)$r['id']; ?>">
                      <div class="col-md-2"><input class="form-control" name="code" value="<?php echo htmlspecialchars($r['code']); ?>" required></div>
                      <div class="col-md-2">
                        <select class="form-select" name="type">
                          <option value="percent" <?php echo $r['type']==='percent'?'selected':''; ?>>% نسبة</option>
                          <option value="fixed" <?php echo $r['type']==='fixed'?'selected':''; ?>>قيمة ثابتة</option>
                        </select>
                      </div>
                      <div class="col-md-2"><input class="form-control" name="value" type="number" step="0.01" value="<?php echo (float)$r['value']; ?>" required></div>
                      <div class="col-md-1">
                        <select class="form-select" name="active">
                          <option value="1" <?php echo ((int)$r['active'])?'selected':''; ?>>تشغيل</option>
                          <option value="0" <?php echo ((int)$r['active'])?'':'selected'; ?>>إيقاف</option>
                        </select>
                      </div>
                      <div class="col-md-2"><input class="form-control" name="expires_at" type="datetime-local" value="<?php echo $r['expires_at']?date('Y-m-d\TH:i', strtotime($r['expires_at'])):''; ?>"></div>
                      <div class="col-md-1"><input class="form-control" name="usage_limit" type="number" min="0" value="<?php echo (int)($r['usage_limit'] ?? 0); ?>"></div>
                      <div class="col-md-2"><input class="form-control" name="min_amount" type="number" step="0.01" value="<?php echo (float)($r['min_amount'] ?? 0); ?>" placeholder="حد أدنى"></div>
                      <div class="col-12"><input class="form-control" name="applicable_plans" value="<?php echo $r['applicable_plans']? htmlspecialchars(implode(',', json_decode($r['applicable_plans'], true) ?? [])) : ''; ?>" placeholder="خُطط مسموحة (اختياري)"></div>
                      <div class="col-12"><input class="form-control" name="notes" value="<?php echo htmlspecialchars($r['notes'] ?? ''); ?>" placeholder="ملاحظات"></div>
                      <div class="col-12 text-end"><button class="btn btn-success"><i class="fa fa-save me-1"></i>تحديث</button></div>
                    </form>
                  </td>
                </tr>
              <?php endforeach; ?>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<?php render_footer(); ?>

