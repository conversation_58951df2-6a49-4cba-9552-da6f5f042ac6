<?php
/**
 * ترحيل قاعدة البيانات - دعم تكامل الكوبونات مع الولاء
 * Database Migration - Coupon Loyalty Integration Support
 */

require_once __DIR__ . '/../config/database.php';

try {
    echo "🔄 بدء ترحيل قاعدة البيانات...\n";
    echo "Starting database migration...\n\n";
    
    // 1. إضافة جدول ربط الكوبونات بالولاء
    echo "1️⃣ إنشاء جدول coupon_loyalty_mapping...\n";
    $pdo->exec("CREATE TABLE IF NOT EXISTS coupon_loyalty_mapping (
        id INT AUTO_INCREMENT PRIMARY KEY,
        coupon_id INT NOT NULL UNIQUE,
        loyalty_points_required INT DEFAULT 0,
        loyalty_points_earned INT DEFAULT 0,
        loyalty_multiplier DECIMAL(3,2) DEFAULT 1.00,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (coupon_id) REFERENCES coupons(id) ON DELETE CASCADE,
        INDEX idx_points_required (loyalty_points_required)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ تم إنشاء جدول coupon_loyalty_mapping\n\n";
    
    // 2. إضافة أعمدة إلى جدول الكوبونات
    echo "2️⃣ إضافة أعمدة إلى جدول coupons...\n";
    
    // التحقق من وجود العمود loyalty_multiplier
    $checkColumn = $pdo->query("SHOW COLUMNS FROM coupons LIKE 'loyalty_multiplier'");
    if ($checkColumn->rowCount() === 0) {
        $pdo->exec("ALTER TABLE coupons ADD COLUMN loyalty_multiplier DECIMAL(3,2) DEFAULT 1.00");
        echo "✅ تم إضافة عمود loyalty_multiplier\n";
    }
    
    // التحقق من وجود العمود loyalty_enabled
    $checkColumn = $pdo->query("SHOW COLUMNS FROM coupons LIKE 'loyalty_enabled'");
    if ($checkColumn->rowCount() === 0) {
        $pdo->exec("ALTER TABLE coupons ADD COLUMN loyalty_enabled TINYINT(1) DEFAULT 1");
        echo "✅ تم إضافة عمود loyalty_enabled\n";
    }
    
    echo "\n";
    
    // 3. إضافة أعمدة إلى جدول loyalty_points_ledger
    echo "3️⃣ إضافة أعمدة إلى جدول loyalty_points_ledger...\n";
    
    // التحقق من وجود العمود coupon_id
    $checkColumn = $pdo->query("SHOW COLUMNS FROM loyalty_points_ledger LIKE 'coupon_id'");
    if ($checkColumn->rowCount() === 0) {
        $pdo->exec("ALTER TABLE loyalty_points_ledger ADD COLUMN coupon_id INT NULL");
        echo "✅ تم إضافة عمود coupon_id\n";
    }
    
    // التحقق من وجود العمود transaction_type
    $checkColumn = $pdo->query("SHOW COLUMNS FROM loyalty_points_ledger LIKE 'transaction_type'");
    if ($checkColumn->rowCount() === 0) {
        $pdo->exec("ALTER TABLE loyalty_points_ledger ADD COLUMN transaction_type VARCHAR(50) DEFAULT 'general'");
        echo "✅ تم إضافة عمود transaction_type\n";
    }
    
    echo "\n";
    
    // 4. إنشاء جدول loyalty_tiers إذا لم يكن موجوداً
    echo "4️⃣ إنشاء جدول loyalty_tiers...\n";
    $pdo->exec("CREATE TABLE IF NOT EXISTS loyalty_tiers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) NOT NULL,
        name_ar VARCHAR(50) NOT NULL,
        min_points INT NOT NULL DEFAULT 0,
        max_points INT NULL,
        multiplier DECIMAL(3,2) DEFAULT 1.00,
        benefits JSON NULL,
        color VARCHAR(7) DEFAULT '#007bff',
        icon VARCHAR(50) DEFAULT 'star',
        status ENUM('active','inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_tier_name (name)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ تم إنشاء جدول loyalty_tiers\n\n";
    
    // 5. إنشاء جدول loyalty_points إذا لم يكن موجوداً
    echo "5️⃣ إنشاء جدول loyalty_points...\n";
    $pdo->exec("CREATE TABLE IF NOT EXISTS loyalty_points (
        id INT AUTO_INCREMENT PRIMARY KEY,
        player_id INT NOT NULL UNIQUE,
        points_balance INT DEFAULT 0,
        total_earned INT DEFAULT 0,
        total_redeemed INT DEFAULT 0,
        last_transaction_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (player_id) REFERENCES players(id) ON DELETE CASCADE,
        INDEX idx_points_balance (points_balance)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ تم إنشاء جدول loyalty_points\n\n";
    
    // 6. إنشاء الفهارس
    echo "6️⃣ إنشاء الفهارس...\n";
    
    // فهرس على coupon_id في loyalty_points_ledger
    $checkIndex = $pdo->query("SHOW INDEX FROM loyalty_points_ledger WHERE Key_name = 'idx_coupon_id'");
    if ($checkIndex->rowCount() === 0) {
        $pdo->exec("ALTER TABLE loyalty_points_ledger ADD INDEX idx_coupon_id (coupon_id)");
        echo "✅ تم إضافة فهرس idx_coupon_id\n";
    }
    
    // فهرس على player_id و created_at
    $checkIndex = $pdo->query("SHOW INDEX FROM loyalty_points_ledger WHERE Key_name = 'idx_player_created'");
    if ($checkIndex->rowCount() === 0) {
        $pdo->exec("ALTER TABLE loyalty_points_ledger ADD INDEX idx_player_created (player_id, created_at)");
        echo "✅ تم إضافة فهرس idx_player_created\n";
    }
    
    echo "\n";
    
    // 7. إدراج مستويات الولاء الافتراضية
    echo "7️⃣ إدراج مستويات الولاء الافتراضية...\n";
    
    $checkTiers = $pdo->query("SELECT COUNT(*) as count FROM loyalty_tiers")->fetch(PDO::FETCH_ASSOC);
    if ($checkTiers['count'] == 0) {
        $tiers = [
            ['Bronze', 'برونزي', 0, 499, 1.00, 'star'],
            ['Silver', 'فضي', 500, 999, 1.25, 'star'],
            ['Gold', 'ذهبي', 1000, 4999, 1.50, 'star'],
            ['Platinum', 'بلاتيني', 5000, NULL, 2.00, 'crown']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO loyalty_tiers (name, name_ar, min_points, max_points, multiplier, icon) VALUES (?, ?, ?, ?, ?, ?)");
        foreach ($tiers as $tier) {
            $stmt->execute($tier);
        }
        echo "✅ تم إدراج مستويات الولاء الافتراضية\n";
    }
    
    echo "\n✅ تم إكمال الترحيل بنجاح!\n";
    echo "✅ Migration completed successfully!\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في الترحيل: " . $e->getMessage() . "\n";
    echo "❌ Migration error: " . $e->getMessage() . "\n";
    exit(1);
}
?>

