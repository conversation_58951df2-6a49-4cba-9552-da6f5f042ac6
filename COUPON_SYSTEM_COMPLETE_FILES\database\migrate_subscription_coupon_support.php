<?php
/**
 * سكريبت ترحيل - إضافة دعم الكوبونات لنظام الاشتراكات
 * Migration Script - Add Coupon Support to Subscription System
 */

require_once __DIR__ . '/../config.php';

echo "🔄 بدء ترحيل قاعدة البيانات - إضافة دعم الكوبونات للاشتراكات\n";
echo "=" . str_repeat("=", 70) . "\n\n";

try {
    $pdo = getDBConnection();
    
    // 1. إضافة أعمدة الكوبونات لجدول player_subscriptions
    echo "📝 إضافة أعمدة الكوبونات لجدول player_subscriptions...\n";
    
    $alterStatements = [
        "ALTER TABLE `player_subscriptions` ADD COLUMN IF NOT EXISTS `coupon_id` BIGINT UNSIGNED NULL COMMENT 'معرف الكوبون'",
        "ALTER TABLE `player_subscriptions` ADD COLUMN IF NOT EXISTS `coupon_code` VARCHAR(50) NULL COMMENT 'كود الكوبون'",
        "ALTER TABLE `player_subscriptions` ADD COLUMN IF NOT EXISTS `original_amount` DECIMAL(10,2) DEFAULT 0 COMMENT 'المبلغ الأصلي قبل الخصم'",
        "ALTER TABLE `player_subscriptions` ADD COLUMN IF NOT EXISTS `coupon_discount_amount` DECIMAL(10,2) DEFAULT 0 COMMENT 'مبلغ الخصم من الكوبون'",
        "ALTER TABLE `player_subscriptions` ADD COLUMN IF NOT EXISTS `final_amount` DECIMAL(10,2) NULL COMMENT 'المبلغ النهائي بعد الخصم'",
        "ALTER TABLE `player_subscriptions` ADD COLUMN IF NOT EXISTS `coupon_applied_at` TIMESTAMP NULL COMMENT 'وقت تطبيق الكوبون'",
        "ALTER TABLE `player_subscriptions` ADD COLUMN IF NOT EXISTS `coupon_applied_by` INT UNSIGNED NULL COMMENT 'معرف المستخدم الذي طبق الكوبون'",
    ];
    
    foreach ($alterStatements as $sql) {
        try {
            $pdo->exec($sql);
            echo "  ✅ " . substr($sql, 0, 60) . "...\n";
        } catch (Exception $e) {
            echo "  ⚠️  " . substr($sql, 0, 60) . "... (قد يكون موجوداً)\n";
        }
    }
    
    // 2. إضافة فهارس للأداء
    echo "\n📊 إضافة فهارس للأداء...\n";
    
    $indexStatements = [
        "ALTER TABLE `player_subscriptions` ADD INDEX IF NOT EXISTS `idx_coupon_id` (`coupon_id`)",
        "ALTER TABLE `player_subscriptions` ADD INDEX IF NOT EXISTS `idx_coupon_code` (`coupon_code`)",
        "ALTER TABLE `player_subscriptions` ADD INDEX IF NOT EXISTS `idx_coupon_applied` (`coupon_applied_at`)",
    ];
    
    foreach ($indexStatements as $sql) {
        try {
            $pdo->exec($sql);
            echo "  ✅ " . substr($sql, 0, 60) . "...\n";
        } catch (Exception $e) {
            echo "  ⚠️  " . substr($sql, 0, 60) . "... (قد يكون موجوداً)\n";
        }
    }
    
    // 3. إضافة علاقات Foreign Key
    echo "\n🔗 إضافة علاقات قاعدة البيانات...\n";
    
    try {
        $pdo->exec("ALTER TABLE `player_subscriptions` ADD CONSTRAINT `fk_subscription_coupon` 
                   FOREIGN KEY (`coupon_id`) REFERENCES `coupons` (`id`) 
                   ON DELETE SET NULL ON UPDATE CASCADE");
        echo "  ✅ تم إضافة علاقة Foreign Key للكوبونات\n";
    } catch (Exception $e) {
        echo "  ⚠️  علاقة Foreign Key قد تكون موجودة\n";
    }
    
    // 4. التحقق من النتائج
    echo "\n✅ التحقق من النتائج...\n";
    
    $stmt = $pdo->query("DESCRIBE player_subscriptions");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $columnNames = array_column($columns, 'Field');
    
    $requiredColumns = ['coupon_id', 'coupon_code', 'original_amount', 'coupon_discount_amount', 'final_amount'];
    $allPresent = true;
    
    foreach ($requiredColumns as $col) {
        if (in_array($col, $columnNames)) {
            echo "  ✅ العمود '$col' موجود\n";
        } else {
            echo "  ❌ العمود '$col' غير موجود\n";
            $allPresent = false;
        }
    }
    
    echo "\n" . str_repeat("=", 70) . "\n";
    
    if ($allPresent) {
        echo "✅ تم إكمال الترحيل بنجاح!\n";
        echo "📊 جميع الأعمدة والفهارس والعلاقات تم إضافتها بنجاح\n";
    } else {
        echo "⚠️  تم إكمال الترحيل مع بعض التحذيرات\n";
    }
    
    echo "\n🎉 الترحيل اكتمل!\n";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
    exit(1);
}
?>

