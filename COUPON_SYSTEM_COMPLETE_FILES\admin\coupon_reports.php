<?php
/**
 * تقارير الكوبونات - Coupon Reports
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../includes/layout.php';
require_once __DIR__ . '/../includes/CouponReportingSystem.php';

require_roles(['admin', 'super_admin']);

$pdo = getDBConnection();
$reportingSystem = new CouponReportingSystem($pdo);

// الحصول على التقارير
$reports = [];
try {
    $reports['usage'] = $reportingSystem->getUsageReport();
    $reports['performance'] = $reportingSystem->getPerformanceReport();
    $reports['revenue'] = $reportingSystem->getRevenueReport();
} catch (Exception $e) {}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقارير الكوبونات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f7fa; }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header { margin-bottom: 30px; }
        .header h1 { color: #2c3e50; font-size: 28px; margin-bottom: 10px; }
        
        .reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .report-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .report-title {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 20px;
        }
        
        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .stats-table th {
            background: #ecf0f1;
            padding: 10px;
            text-align: right;
            color: #2c3e50;
            font-weight: 600;
            border-bottom: 2px solid #bdc3c7;
        }
        
        .stats-table td {
            padding: 10px;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .stats-table tr:hover {
            background: #f9f9f9;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s;
        }
        
        .btn-primary { background: #3498db; color: white; }
        .btn-primary:hover { background: #2980b9; }
        .btn-export { background: #27ae60; color: white; }
        .btn-export:hover { background: #229954; }
        
        .actions-bar {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .metric-label { color: #7f8c8d; }
        .metric-value { font-weight: bold; color: #2c3e50; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-chart-bar"></i> تقارير الكوبونات</h1>
        </div>
        
        <!-- شريط الإجراءات -->
        <div class="actions-bar">
            <button class="btn btn-export" onclick="exportReport()">
                <i class="fas fa-download"></i> تصدير التقرير
            </button>
            <button class="btn btn-primary" onclick="printReport()">
                <i class="fas fa-print"></i> طباعة
            </button>
            <a href="coupon_dashboard.php" class="btn btn-primary">
                <i class="fas fa-arrow-left"></i> العودة
            </a>
        </div>
        
        <!-- التقارير -->
        <div class="reports-grid">
            <!-- تقرير الاستخدام -->
            <div class="report-card">
                <div class="report-title">
                    <i class="fas fa-chart-line"></i> تقرير الاستخدام
                </div>
                <div class="metric">
                    <span class="metric-label">إجمالي الاستخدام</span>
                    <span class="metric-value"><?php echo $reports['usage']['total_usage'] ?? 0; ?></span>
                </div>
                <div class="metric">
                    <span class="metric-label">متوسط الاستخدام</span>
                    <span class="metric-value"><?php echo round($reports['usage']['average_usage'] ?? 0, 2); ?></span>
                </div>
                <div class="metric">
                    <span class="metric-label">الكوبون الأكثر استخداماً</span>
                    <span class="metric-value"><?php echo $reports['usage']['most_used_coupon'] ?? 'N/A'; ?></span>
                </div>
                <div class="metric">
                    <span class="metric-label">معدل الاستخدام</span>
                    <span class="metric-value"><?php echo round($reports['usage']['usage_rate'] ?? 0, 2); ?>%</span>
                </div>
            </div>
            
            <!-- تقرير الأداء -->
            <div class="report-card">
                <div class="report-title">
                    <i class="fas fa-tachometer-alt"></i> تقرير الأداء
                </div>
                <div class="metric">
                    <span class="metric-label">الكوبونات النشطة</span>
                    <span class="metric-value"><?php echo $reports['performance']['active_coupons'] ?? 0; ?></span>
                </div>
                <div class="metric">
                    <span class="metric-label">الكوبونات المنتهية</span>
                    <span class="metric-value"><?php echo $reports['performance']['expired_coupons'] ?? 0; ?></span>
                </div>
                <div class="metric">
                    <span class="metric-label">معدل النشاط</span>
                    <span class="metric-value"><?php echo round($reports['performance']['activity_rate'] ?? 0, 2); ?>%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">الكوبونات المستخدمة بالكامل</span>
                    <span class="metric-value"><?php echo $reports['performance']['fully_used'] ?? 0; ?></span>
                </div>
            </div>
            
            <!-- تقرير الإيرادات -->
            <div class="report-card">
                <div class="report-title">
                    <i class="fas fa-money-bill-wave"></i> تقرير الإيرادات
                </div>
                <div class="metric">
                    <span class="metric-label">إجمالي الخصم</span>
                    <span class="metric-value"><?php echo number_format($reports['revenue']['total_discount'] ?? 0, 2); ?> ريال</span>
                </div>
                <div class="metric">
                    <span class="metric-label">متوسط الخصم</span>
                    <span class="metric-value"><?php echo number_format($reports['revenue']['average_discount'] ?? 0, 2); ?> ريال</span>
                </div>
                <div class="metric">
                    <span class="metric-label">أكبر خصم</span>
                    <span class="metric-value"><?php echo number_format($reports['revenue']['max_discount'] ?? 0, 2); ?> ريال</span>
                </div>
                <div class="metric">
                    <span class="metric-label">تأثير الخصم</span>
                    <span class="metric-value"><?php echo round($reports['revenue']['discount_impact'] ?? 0, 2); ?>%</span>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function exportReport() {
            alert('سيتم تصدير التقرير قريباً');
        }
        
        function printReport() {
            window.print();
        }
    </script>
</body>
</html>

