# ⚡ مرجع سريع - تكامل الكوبونات مع الاشتراكات
# Quick Reference - Coupon Subscription Integration

## 🚀 البدء السريع | Quick Start

### 1. تشغيل الترحيل
```bash
php database/migrate_subscription_coupon_support.php
```

### 2. تشغيل الاختبارات
```bash
php tests/test_subscription_integration.php
```

### 3. الوصول إلى الإدارة
```
http://system.c7c.club/admin/coupon_subscription_integration.php
```

## 📝 أمثلة الكود | Code Examples

### إنشاء كائنات التكامل
```php
$couponManager = new CouponManager($pdo);
$couponValidator = new CouponValidator($pdo, $couponManager);
$subscriptionIntegration = new CouponSubscriptionIntegration(
    $pdo, 
    $couponManager, 
    $couponValidator
);
```

### حساب السعر
```php
$result = $subscriptionIntegration->calculateSubscriptionPrice(
    planId: 1,
    couponCode: 'SUMMER20',
    userId: 123
);
```

### إنشاء اشتراك مع كوبون
```php
$result = $subscriptionIntegration->applyToNewSubscription([
    'plan_id' => 1,
    'player_id' => 456,
    'user_id' => 123,
    'coupon_code' => 'SUMMER20'
]);
```

### تجديد الاشتراك
```php
$result = $subscriptionIntegration->applyToRenewal(
    subscriptionId: 789,
    couponCode: 'RENEWAL10'
);
```

### ترقية الاشتراك
```php
$result = $subscriptionIntegration->applyToUpgrade(
    subscriptionId: 789,
    newPlanId: 2,
    couponCode: 'UPGRADE15'
);
```

### إزالة الكوبون
```php
$result = $subscriptionIntegration->removeCoupon(subscriptionId: 789);
```

## 🔌 أوامر API | API Commands

### حساب السعر
```bash
curl -X POST http://system.c7c.club/api/coupons_subscription_integration.php \
  -d "action=calculate_price" \
  -d "plan_id=1" \
  -d "coupon_code=SUMMER20"
```

### اشتراك جديد
```bash
curl -X POST http://system.c7c.club/api/coupons_subscription_integration.php \
  -d "action=apply_to_new" \
  -d "plan_id=1" \
  -d "player_id=456" \
  -d "coupon_code=SUMMER20"
```

### تجديد
```bash
curl -X POST http://system.c7c.club/api/coupons_subscription_integration.php \
  -d "action=apply_to_renewal" \
  -d "subscription_id=789" \
  -d "coupon_code=RENEWAL10"
```

### ترقية
```bash
curl -X POST http://system.c7c.club/api/coupons_subscription_integration.php \
  -d "action=apply_to_upgrade" \
  -d "subscription_id=789" \
  -d "new_plan_id=2" \
  -d "coupon_code=UPGRADE15"
```

### إزالة الكوبون
```bash
curl -X POST http://system.c7c.club/api/coupons_subscription_integration.php \
  -d "action=remove_coupon" \
  -d "subscription_id=789"
```

### التحقق من الصحة
```bash
curl -X POST http://system.c7c.club/api/coupons_subscription_integration.php \
  -d "action=validate_for_subscription" \
  -d "coupon_code=SUMMER20" \
  -d "plan_id=1"
```

## 📊 استعلامات SQL | SQL Queries

### الحصول على الاشتراكات بكوبونات
```sql
SELECT * FROM player_subscriptions 
WHERE coupon_id IS NOT NULL 
ORDER BY coupon_applied_at DESC;
```

### الحصول على الاشتراكات النشطة
```sql
SELECT ps.*, sp.name_ar, sp.price 
FROM player_subscriptions ps
LEFT JOIN subscription_plans sp ON ps.plan_id = sp.id
WHERE ps.status = 'active';
```

### إجمالي الخصومات
```sql
SELECT 
    SUM(coupon_discount_amount) as total_discount,
    COUNT(*) as total_subscriptions
FROM player_subscriptions 
WHERE coupon_id IS NOT NULL;
```

## 📁 الملفات الرئيسية | Main Files

| الملف | الوصف |
|------|-------|
| `includes/CouponSubscriptionIntegration.php` | فئة التكامل الرئيسية |
| `api/coupons_subscription_integration.php` | واجهات API |
| `admin/coupon_subscription_integration.php` | واجهة الإدارة |
| `database/migrate_subscription_coupon_support.php` | سكريبت الترحيل |
| `tests/test_subscription_integration.php` | الاختبارات |
| `docs/SUBSCRIPTION_INTEGRATION_GUIDE.md` | الدليل الشامل |

## ✅ قائمة التحقق | Checklist

- [ ] تشغيل الترحيل
- [ ] التحقق من الأعمدة
- [ ] تشغيل الاختبارات
- [ ] اختبار API
- [ ] اختبار الإدارة
- [ ] التحقق من السجلات

## 🔍 استكشاف الأخطاء | Troubleshooting

| المشكلة | الحل |
|--------|------|
| الكوبون لا يطبق | تحقق من صحة الكوبون والتاريخ |
| خطأ في قاعدة البيانات | شغّل سكريبت الترحيل |
| خطأ في الصلاحيات | تحقق من دور المستخدم |
| API لا يعمل | تحقق من المصادقة والجلسة |

## 📞 روابط مفيدة | Useful Links

- [دليل الكوبونات الشامل](COUPON_SYSTEM_DOCUMENTATION.md)
- [دليل التكامل](COUPON_INTEGRATION_GUIDE.md)
- [فئة CouponSubscriptionIntegration](../includes/CouponSubscriptionIntegration.php)

---

**آخر تحديث:** 2025-12-28
**الإصدار:** 1.0

