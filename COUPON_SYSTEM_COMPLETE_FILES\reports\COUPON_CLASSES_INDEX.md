# فهرس فئات الكوبونات
# Coupon Classes Index

---

## 📚 الفئات الأساسية

### 1. CouponManager
**الملف:** `includes/CouponManager.php`
**الأسطر:** 255
**الوظائف:** 9

**الوصف:** فئة إدارة الكوبونات الرئيسية

**الوظائف الرئيسية:**
```php
createCoupon(array $data): array
updateCoupon(int $couponId, array $data): array
getCoupon(int $couponId): ?array
getCouponByCode(string $code): ?array
validateCoupon(string $code, float $amount, ?int $userId, ?int $planId): array
calculateDiscount(array $coupon, float $amount): float
applyCoupon(int $couponId, int $userId, float $originalAmount, ?int $subscriptionId, ?int $paymentId): array
logAudit(int $couponId, string $action, ?array $oldValues, ?array $newValues, ?int $performedBy): void
generateUUID(): string
```

---

### 2. CouponValidator
**الملف:** `includes/CouponValidator.php`
**الأسطر:** 226
**الوظائف:** 9

**الوصف:** فئة التحقق المتقدم من الكوبونات

**الوظائف الرئيسية:**
```php
validate(string $code, array $context): array
validateDates(array $coupon): array
validateUsageLimit(array $coupon): array
validateUserUsageLimit(array $coupon, int $userId): array
validateMinimumAmount(array $coupon, float $amount): array
validateUserRestrictions(array $coupon, array $context): array
validateApplicablePlans(array $coupon, int $planId): array
isNewMember(int $userId): bool
isVIPMember(int $userId): bool
```

---

### 3. CouponPaymentIntegration
**الملف:** `includes/CouponPaymentIntegration.php`
**الأسطر:** 250
**الوظائف:** 6

**الوصف:** تكامل الكوبونات مع نظام الدفع

**الوظائف الرئيسية:**
```php
processPaymentWithCoupon(array $paymentData): array
applyToInvoice(int $invoiceId, string $couponCode, int $userId): array
applyToPayment(int $paymentId, string $couponCode, int $userId): array
removeCouponFromPayment(int $paymentId): array
getInvoice(int $invoiceId): ?array
getPayment(int $paymentId): ?array
```

---

### 4. CouponSubscriptionIntegration
**الملف:** `includes/CouponSubscriptionIntegration.php`
**الأسطر:** 258
**الوظائف:** 5

**الوصف:** تكامل الكوبونات مع نظام الاشتراكات

**الوظائف الرئيسية:**
```php
calculateSubscriptionPrice(int $planId, ?string $couponCode, int $userId): array
applyToNewSubscription(array $subscriptionData): array
applyToRenewal(int $subscriptionId, ?string $couponCode): array
getPlan(int $planId): ?array
getSubscription(int $subscriptionId): ?array
```

---

### 5. CouponLoyaltyIntegration
**الملف:** `includes/CouponLoyaltyIntegration.php`
**الأسطر:** 222
**الوظائف:** 8

**الوصف:** تكامل الكوبونات مع نظام الولاء

**الوظائف الرئيسية:**
```php
awardLoyaltyPoints(int $couponId, int $userId, float $discountAmount): array
redeemCouponWithLoyaltyPoints(int $couponId, int $userId): array
createLoyaltyCoupon(array $couponData, array $loyaltyData): array
getUserLoyaltyStats(int $userId): array
getLoyaltyMapping(int $couponId): ?array
addLoyaltyPoints(int $userId, int $points, string $source, string $description): void
deductLoyaltyPoints(int $userId, int $points, string $source, string $description): void
updateLoyaltyBalance(int $userId): void
```

---

### 6. CouponReportingSystem
**الملف:** `includes/CouponReportingSystem.php`
**الأسطر:** 283
**الوظائف:** 8

**الوصف:** نظام التقارير والإحصائيات

**الوظائف الرئيسية:**
```php
getUsageReport(array $filters): array
getRevenueReport(array $filters): array
getTopCouponsReport(int $limit): array
getTopUsersReport(int $limit): array
getExpiredCouponsReport(): array
getExpiringCouponsReport(int $daysThreshold): array
calculateSummary(array $coupons): array
calculateRevenueSummary(array $dailyReport): array
```

---

## 📋 ملفات التوثيق

| الملف | الوصف |
|------|-------|
| `COUPON_CLASSES_VERIFICATION_REPORT.md` | تقرير التحقق الشامل |
| `COUPON_CLASSES_COMPLETION_SUMMARY.md` | ملخص الإنجاز |
| `COUPON_CLASSES_INDEX.md` | هذا الفهرس |

---

## 🔗 الروابط بين الفئات

```
CouponManager (الأساس)
    ↓
CouponValidator (التحقق)
    ↓
├─ CouponPaymentIntegration (الدفع)
├─ CouponSubscriptionIntegration (الاشتراكات)
├─ CouponLoyaltyIntegration (الولاء)
└─ CouponReportingSystem (التقارير)
```

---

## 💾 قاعدة البيانات

**الملف:** `database/coupons_system_schema.sql`

**الجداول:**
- `coupons` - الكوبونات الرئيسية
- `coupon_usage` - سجل الاستخدام
- `coupon_plan_mapping` - ربط الخطط
- `coupon_loyalty_mapping` - ربط الولاء
- `coupon_audit_log` - سجل التدقيق

---

## 🎯 الحالة

✅ **مكتملة بنجاح**

جميع الفئات تم تطويرها وتوثيقها وجاهزة للاستخدام الفوري.

---

**آخر تحديث:** 2025-12-28

