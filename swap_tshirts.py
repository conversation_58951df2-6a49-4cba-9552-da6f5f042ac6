#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لتبديل التيشرت بين صورتين
T-shirt swapping script between two images
"""

import cv2
import numpy as np
from PIL import Image, ImageFilter
import os

def load_and_resize_images(img1_path, img2_path, target_size=(800, 600)):
    """تحميل وتغيير حجم الصور"""
    # تحميل الصور باستخدام PIL أولاً للتعامل مع الأحرف العربية
    try:
        # استخدام PIL لتحميل الصور
        pil_img1 = Image.open(img1_path)
        pil_img2 = Image.open(img2_path)

        # تحويل إلى numpy arrays
        img1 = cv2.cvtColor(np.array(pil_img1), cv2.COLOR_RGB2BGR)
        img2 = cv2.cvtColor(np.array(pil_img2), cv2.COLOR_RGB2BGR)

    except Exception as e:
        print(f"خطأ في تحميل الصور: {e}")
        raise ValueError("لا يمكن تحميل إحدى الصور")

    if img1 is None or img2 is None:
        raise ValueError("لا يمكن تحميل إحدى الصور")

    # تغيير الحجم للمعالجة
    img1_resized = cv2.resize(img1, target_size)
    img2_resized = cv2.resize(img2, target_size)

    return img1_resized, img2_resized

def detect_tshirt_region(image):
    """كشف منطقة التيشرت في الصورة"""
    # تحويل إلى HSV للكشف عن الألوان بشكل أفضل
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # إنشاء قناع للألوان الفاتحة (التيشرت عادة ما يكون فاتح اللون)
    # نطاق واسع للألوان
    lower_bound = np.array([0, 0, 50])
    upper_bound = np.array([180, 100, 255])
    mask = cv2.inRange(hsv, lower_bound, upper_bound)
    
    # تطبيق عمليات مورفولوجية لتنظيف القناع
    kernel = np.ones((5,5), np.uint8)
    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
    mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
    
    # العثور على أكبر منطقة متصلة (التيشرت المحتمل)
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if contours:
        # أكبر منطقة
        largest_contour = max(contours, key=cv2.contourArea)
        
        # إنشاء قناع للتيشرت
        tshirt_mask = np.zeros(mask.shape, dtype=np.uint8)
        cv2.fillPoly(tshirt_mask, [largest_contour], 255)
        
        # تطبيق تنعيم على الحواف
        tshirt_mask = cv2.GaussianBlur(tshirt_mask, (15, 15), 0)
        
        return tshirt_mask, largest_contour
    
    return None, None

def create_better_mask(image, center_region_factor=0.6):
    """إنشاء قناع أفضل للتيشرت بناءً على المنطقة المركزية"""
    height, width = image.shape[:2]
    
    # تحديد المنطقة المركزية (منطقة الصدر المحتملة)
    center_x, center_y = width // 2, height // 2
    region_width = int(width * center_region_factor)
    region_height = int(height * center_region_factor)
    
    # إنشاء قناع بيضاوي للمنطقة المركزية
    mask = np.zeros((height, width), dtype=np.uint8)
    
    # رسم شكل بيضاوي في المنطقة المركزية
    cv2.ellipse(mask, 
                (center_x, center_y), 
                (region_width//2, region_height//2), 
                0, 0, 360, 255, -1)
    
    # تطبيق تنعيم للحصول على انتقال طبيعي
    mask = cv2.GaussianBlur(mask, (51, 51), 0)
    
    return mask

def swap_tshirts(img1, img2):
    """تبديل التيشرت بين الصورتين"""
    # إنشاء أقنعة للمنطقة المركزية (التيشرت)
    mask1 = create_better_mask(img1)
    mask2 = create_better_mask(img2)
    
    # تحويل الأقنعة إلى نطاق 0-1
    mask1_norm = mask1.astype(np.float32) / 255.0
    mask2_norm = mask2.astype(np.float32) / 255.0
    
    # إنشاء أقنعة ثلاثية الأبعاد
    mask1_3d = np.stack([mask1_norm] * 3, axis=2)
    mask2_3d = np.stack([mask2_norm] * 3, axis=2)
    
    # تبديل المناطق
    # استخراج منطقة التيشرت من كل صورة
    tshirt1 = img1.astype(np.float32) * mask1_3d
    tshirt2 = img2.astype(np.float32) * mask2_3d
    
    # إنشاء الخلفيات (بدون التيشرت)
    background1 = img1.astype(np.float32) * (1 - mask1_3d)
    background2 = img2.astype(np.float32) * (1 - mask2_3d)
    
    # دمج التيشرت من الصورة الثانية مع خلفية الصورة الأولى
    result1 = (background1 + tshirt2).astype(np.uint8)
    result2 = (background2 + tshirt1).astype(np.uint8)
    
    return result1, result2

def main():
    """الدالة الرئيسية"""
    # مسارات الصور
    img1_path = "مجلد جديد/1000015537.jpg"
    img2_path = "مجلد جديد/145494654654165.png"
    
    try:
        print("جاري تحميل الصور...")
        img1, img2 = load_and_resize_images(img1_path, img2_path)
        
        print("جاري تبديل التيشرت...")
        result1, result2 = swap_tshirts(img1, img2)
        
        # حفظ النتائج
        output1_path = "مجلد جديد/result1_swapped.jpg"
        output2_path = "مجلد جديد/result2_swapped.jpg"
        
        cv2.imwrite(output1_path, result1)
        cv2.imwrite(output2_path, result2)
        
        print(f"تم حفظ النتائج في:")
        print(f"- {output1_path}")
        print(f"- {output2_path}")
        
        # عرض معلومات إضافية
        print(f"\nمعلومات الصور:")
        print(f"الصورة الأولى: {img1.shape}")
        print(f"الصورة الثانية: {img2.shape}")
        
    except Exception as e:
        print(f"حدث خطأ: {e}")

if __name__ == "__main__":
    main()
