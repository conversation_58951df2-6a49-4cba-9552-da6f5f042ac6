<?php
/**
 * تكامل الكوبونات مع نظام الاشتراكات - واجهات API
 * Coupon Subscription Integration - API Endpoints
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/CouponManager.php';
require_once __DIR__ . '/../includes/CouponValidator.php';
require_once __DIR__ . '/../includes/CouponSubscriptionIntegration.php';

header('Content-Type: application/json; charset=utf-8');

try {
    $pdo = getDBConnection();
    $action = $_REQUEST['action'] ?? '';
    
    // التحقق من المصادقة
    if (!isset($_SESSION['user_id'])) {
        throw new Exception('غير مصرح - يجب تسجيل الدخول');
    }
    
    $userId = (int)$_SESSION['user_id'];
    
    // إنشاء الكائنات
    $couponManager = new CouponManager($pdo);
    $couponValidator = new CouponValidator($pdo, $couponManager);
    $subscriptionIntegration = new CouponSubscriptionIntegration($pdo, $couponManager, $couponValidator);
    
    switch ($action) {
        case 'calculate_price':
            // حساب سعر الاشتراك مع الكوبون
            $planId = (int)($_POST['plan_id'] ?? 0);
            $couponCode = $_POST['coupon_code'] ?? null;
            
            if (!$planId) {
                throw new Exception('معرف الخطة مطلوب');
            }
            
            $result = $subscriptionIntegration->calculateSubscriptionPrice($planId, $couponCode, $userId);
            echo json_encode($result);
            break;
            
        case 'apply_to_new':
            // تطبيق الكوبون على اشتراك جديد
            $planId = (int)($_POST['plan_id'] ?? 0);
            $playerId = (int)($_POST['player_id'] ?? 0);
            $couponCode = $_POST['coupon_code'] ?? null;
            
            if (!$planId || !$playerId) {
                throw new Exception('معرف الخطة ومعرف اللاعب مطلوبان');
            }
            
            $subscriptionData = [
                'plan_id' => $planId,
                'player_id' => $playerId,
                'user_id' => $userId,
                'coupon_code' => $couponCode
            ];
            
            $result = $subscriptionIntegration->applyToNewSubscription($subscriptionData);
            echo json_encode($result);
            break;
            
        case 'apply_to_renewal':
            // تطبيق الكوبون على تجديد الاشتراك
            $subscriptionId = (int)($_POST['subscription_id'] ?? 0);
            $couponCode = $_POST['coupon_code'] ?? null;
            
            if (!$subscriptionId) {
                throw new Exception('معرف الاشتراك مطلوب');
            }
            
            $result = $subscriptionIntegration->applyToRenewal($subscriptionId, $couponCode);
            echo json_encode($result);
            break;
            
        case 'apply_to_upgrade':
            // تطبيق الكوبون على ترقية الاشتراك
            $subscriptionId = (int)($_POST['subscription_id'] ?? 0);
            $newPlanId = (int)($_POST['new_plan_id'] ?? 0);
            $couponCode = $_POST['coupon_code'] ?? null;
            
            if (!$subscriptionId || !$newPlanId) {
                throw new Exception('معرف الاشتراك والخطة الجديدة مطلوبان');
            }
            
            $result = $subscriptionIntegration->applyToUpgrade($subscriptionId, $newPlanId, $couponCode);
            echo json_encode($result);
            break;
            
        case 'remove_coupon':
            // إزالة الكوبون من الاشتراك
            $subscriptionId = (int)($_POST['subscription_id'] ?? 0);
            
            if (!$subscriptionId) {
                throw new Exception('معرف الاشتراك مطلوب');
            }
            
            $result = $subscriptionIntegration->removeCoupon($subscriptionId);
            echo json_encode($result);
            break;
            
        case 'validate_for_subscription':
            // التحقق من صحة الكوبون للاشتراك
            $couponCode = $_POST['coupon_code'] ?? null;
            $planId = (int)($_POST['plan_id'] ?? 0);
            
            if (!$couponCode || !$planId) {
                throw new Exception('كود الكوبون ومعرف الخطة مطلوبان');
            }
            
            $context = [
                'amount' => 0,
                'plan_id' => $planId,
                'user_id' => $userId
            ];
            
            $validation = $couponValidator->validate($couponCode, $context);
            echo json_encode($validation);
            break;
            
        case 'get_subscription':
            // الحصول على بيانات الاشتراك
            $subscriptionId = (int)($_POST['subscription_id'] ?? 0);
            
            if (!$subscriptionId) {
                throw new Exception('معرف الاشتراك مطلوب');
            }
            
            $subscription = $subscriptionIntegration->getSubscription($subscriptionId);
            echo json_encode([
                'success' => $subscription !== null,
                'subscription' => $subscription
            ]);
            break;
            
        default:
            throw new Exception('إجراء غير معروف: ' . $action);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>

