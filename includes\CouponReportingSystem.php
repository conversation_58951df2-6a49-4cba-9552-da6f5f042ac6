<?php
/**
 * نظام تقارير الكوبونات
 * Coupon Reporting System
 */

class CouponReportingSystem {
    private $pdo;
    
    public function __construct(PDO $pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * تقرير استخدام الكوبونات
     */
    public function getUsageReport(array $filters = []): array {
        try {
            $sql = "SELECT 
                    c.id, c.code, c.name, c.type, c.value,
                    COUNT(cu.id) as usage_count,
                    SUM(cu.discount_amount) as total_discount,
                    SUM(cu.original_amount) as total_original_amount,
                    SUM(cu.final_amount) as total_final_amount,
                    c.usage_limit,
                    c.used_count,
                    c.is_active,
                    c.valid_from,
                    c.valid_until
                    FROM coupons c
                    LEFT JOIN coupon_usage cu ON c.id = cu.coupon_id AND cu.status = 'applied'
                    WHERE 1=1";
            
            $params = [];
            
            if (!empty($filters['coupon_id'])) {
                $sql .= " AND c.id = ?";
                $params[] = $filters['coupon_id'];
            }
            
            if (!empty($filters['start_date'])) {
                $sql .= " AND cu.used_at >= ?";
                $params[] = $filters['start_date'];
            }
            
            if (!empty($filters['end_date'])) {
                $sql .= " AND cu.used_at <= ?";
                $params[] = $filters['end_date'];
            }
            
            if (isset($filters['is_active'])) {
                $sql .= " AND c.is_active = ?";
                $params[] = $filters['is_active'];
            }
            
            $sql .= " GROUP BY c.id ORDER BY c.created_at DESC";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            $coupons = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'success' => true,
                'coupons' => $coupons,
                'summary' => $this->calculateSummary($coupons)
            ];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * تقرير الإيرادات والخصومات
     */
    public function getRevenueReport(array $filters = []): array {
        try {
            $sql = "SELECT 
                    DATE(cu.used_at) as date,
                    COUNT(cu.id) as transaction_count,
                    SUM(cu.original_amount) as gross_revenue,
                    SUM(cu.discount_amount) as total_discount,
                    SUM(cu.final_amount) as net_revenue,
                    AVG(cu.discount_amount) as avg_discount
                    FROM coupon_usage cu
                    WHERE cu.status = 'applied'";
            
            $params = [];
            
            if (!empty($filters['start_date'])) {
                $sql .= " AND cu.used_at >= ?";
                $params[] = $filters['start_date'];
            }
            
            if (!empty($filters['end_date'])) {
                $sql .= " AND cu.used_at <= ?";
                $params[] = $filters['end_date'];
            }
            
            $sql .= " GROUP BY DATE(cu.used_at) ORDER BY date DESC";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            $dailyReport = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'success' => true,
                'daily_report' => $dailyReport,
                'summary' => $this->calculateRevenueSummary($dailyReport)
            ];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * تقرير أفضل الكوبونات
     */
    public function getTopCouponsReport(int $limit = 10): array {
        try {
            $sql = "SELECT 
                    c.id, c.code, c.name, c.type, c.value,
                    COUNT(cu.id) as usage_count,
                    SUM(cu.discount_amount) as total_discount,
                    COUNT(DISTINCT cu.user_id) as unique_users
                    FROM coupons c
                    LEFT JOIN coupon_usage cu ON c.id = cu.coupon_id AND cu.status = 'applied'
                    GROUP BY c.id
                    ORDER BY usage_count DESC
                    LIMIT ?";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$limit]);
            $topCoupons = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'success' => true,
                'top_coupons' => $topCoupons
            ];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * تقرير المستخدمين الأكثر استخداماً للكوبونات
     */
    public function getTopUsersReport(int $limit = 10): array {
        try {
            $sql = "SELECT 
                    u.id, u.name, u.email,
                    COUNT(cu.id) as coupon_usage_count,
                    SUM(cu.discount_amount) as total_discount_received,
                    COUNT(DISTINCT cu.coupon_id) as unique_coupons_used
                    FROM users u
                    LEFT JOIN coupon_usage cu ON u.id = cu.user_id AND cu.status = 'applied'
                    GROUP BY u.id
                    HAVING coupon_usage_count > 0
                    ORDER BY coupon_usage_count DESC
                    LIMIT ?";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$limit]);
            $topUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'success' => true,
                'top_users' => $topUsers
            ];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * تقرير الكوبونات المنتهية الصلاحية
     */
    public function getExpiredCouponsReport(): array {
        try {
            $sql = "SELECT 
                    id, code, name, type, value,
                    valid_until, used_count, usage_limit,
                    DATEDIFF(NOW(), valid_until) as days_expired
                    FROM coupons
                    WHERE valid_until < NOW()
                    ORDER BY valid_until DESC";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            $expiredCoupons = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'success' => true,
                'expired_coupons' => $expiredCoupons,
                'count' => count($expiredCoupons)
            ];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * تقرير الكوبونات القريبة من الانتهاء
     */
    public function getExpiringCouponsReport(int $daysThreshold = 7): array {
        try {
            $sql = "SELECT 
                    id, code, name, type, value,
                    valid_until, used_count, usage_limit,
                    DATEDIFF(valid_until, NOW()) as days_remaining
                    FROM coupons
                    WHERE valid_until > NOW() 
                    AND valid_until <= DATE_ADD(NOW(), INTERVAL ? DAY)
                    AND is_active = 1
                    ORDER BY valid_until ASC";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$daysThreshold]);
            $expiringCoupons = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'success' => true,
                'expiring_coupons' => $expiringCoupons,
                'count' => count($expiringCoupons)
            ];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * حساب ملخص الاستخدام
     */
    private function calculateSummary(array $coupons): array {
        $totalDiscount = 0;
        $totalUsage = 0;
        $activeCoupons = 0;
        
        foreach ($coupons as $coupon) {
            $totalDiscount += (float)$coupon['total_discount'];
            $totalUsage += (int)$coupon['usage_count'];
            if ($coupon['is_active']) {
                $activeCoupons++;
            }
        }
        
        return [
            'total_coupons' => count($coupons),
            'active_coupons' => $activeCoupons,
            'total_usage' => $totalUsage,
            'total_discount' => $totalDiscount,
            'avg_discount_per_coupon' => count($coupons) > 0 ? $totalDiscount / count($coupons) : 0
        ];
    }
    
    /**
     * حساب ملخص الإيرادات
     */
    private function calculateRevenueSummary(array $dailyReport): array {
        $totalGross = 0;
        $totalDiscount = 0;
        $totalNet = 0;
        $totalTransactions = 0;
        
        foreach ($dailyReport as $day) {
            $totalGross += (float)$day['gross_revenue'];
            $totalDiscount += (float)$day['total_discount'];
            $totalNet += (float)$day['net_revenue'];
            $totalTransactions += (int)$day['transaction_count'];
        }
        
        return [
            'total_gross_revenue' => $totalGross,
            'total_discount' => $totalDiscount,
            'total_net_revenue' => $totalNet,
            'total_transactions' => $totalTransactions,
            'discount_percentage' => $totalGross > 0 ? ($totalDiscount / $totalGross) * 100 : 0,
            'avg_discount_per_transaction' => $totalTransactions > 0 ? $totalDiscount / $totalTransactions : 0
        ];
    }
}
?>

