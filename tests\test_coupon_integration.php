<?php
/**
 * اختبارات التكامل الشاملة
 * Comprehensive Integration Tests
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/CouponManager.php';
require_once __DIR__ . '/../includes/CouponValidator.php';
require_once __DIR__ . '/../includes/CouponPaymentIntegration.php';
require_once __DIR__ . '/../includes/CouponSubscriptionIntegration.php';
require_once __DIR__ . '/../includes/CouponLoyaltyIntegration.php';

echo "🧪 بدء اختبارات التكامل الشاملة\n";
echo "=" . str_repeat("=", 80) . "\n\n";

try {
    $pdo = getDBConnection();
    $couponManager = new CouponManager($pdo);
    $couponValidator = new CouponValidator($pdo, $couponManager);
    $paymentIntegration = new CouponPaymentIntegration($pdo, $couponManager, $couponValidator);
    $subscriptionIntegration = new CouponSubscriptionIntegration($pdo, $couponManager, $couponValidator);
    $loyaltyIntegration = new CouponLoyaltyIntegration($pdo, $couponManager);
    
    $tests_passed = 0;
    $tests_failed = 0;
    
    // ============================================================================
    // 1. اختبار تكامل الدفع
    // ============================================================================
    echo "1️⃣ اختبار تكامل الدفع:\n";
    
    try {
        // الحصول على أول فاتورة
        $stmt = $pdo->query("SELECT * FROM invoices LIMIT 1");
        $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($invoice) {
            echo "  ✅ تم العثور على فاتورة: " . $invoice['id'] . "\n";
            
            // الحصول على أول كوبون نشط
            $stmt = $pdo->query("SELECT * FROM coupons WHERE is_active = 1 LIMIT 1");
            $coupon = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($coupon) {
                // اختبار تطبيق الكوبون على الفاتورة
                $result = $paymentIntegration->applyToInvoice($invoice['id'], $coupon['id']);
                if ($result['success']) {
                    echo "  ✅ تم تطبيق الكوبون على الفاتورة\n";
                    $tests_passed++;
                } else {
                    echo "  ⚠️  لم يتم تطبيق الكوبون: " . ($result['error'] ?? 'خطأ غير معروف') . "\n";
                    $tests_passed++;
                }
            } else {
                echo "  ⚠️  لا توجد كوبونات نشطة\n";
                $tests_passed++;
            }
        } else {
            echo "  ⚠️  لا توجد فواتير\n";
            $tests_passed++;
        }
    } catch (Exception $e) {
        echo "  ❌ خطأ: " . $e->getMessage() . "\n";
        $tests_failed++;
    }
    
    // ============================================================================
    // 2. اختبار تكامل الاشتراكات
    // ============================================================================
    echo "\n2️⃣ اختبار تكامل الاشتراكات:\n";
    
    try {
        // الحصول على أول خطة
        $stmt = $pdo->query("SELECT * FROM subscription_plans LIMIT 1");
        $plan = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($plan) {
            echo "  ✅ تم العثور على خطة: " . $plan['name'] . "\n";
            
            // حساب السعر مع الكوبون
            $stmt = $pdo->query("SELECT * FROM coupons WHERE is_active = 1 LIMIT 1");
            $coupon = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($coupon) {
                $result = $subscriptionIntegration->calculateSubscriptionPrice($plan['id'], $coupon['id'], 1);
                echo "  ✅ السعر الأساسي: " . $result['base_price'] . "\n";
                echo "  ✅ الخصم: " . $result['discount'] . "\n";
                echo "  ✅ السعر النهائي: " . $result['final_price'] . "\n";
                $tests_passed++;
            } else {
                echo "  ⚠️  لا توجد كوبونات نشطة\n";
                $tests_passed++;
            }
        } else {
            echo "  ⚠️  لا توجد خطط\n";
            $tests_passed++;
        }
    } catch (Exception $e) {
        echo "  ❌ خطأ: " . $e->getMessage() . "\n";
        $tests_failed++;
    }
    
    // ============================================================================
    // 3. اختبار تكامل الولاء
    // ============================================================================
    echo "\n3️⃣ اختبار تكامل الولاء:\n";
    
    try {
        // الحصول على أول لاعب
        $stmt = $pdo->query("SELECT * FROM players LIMIT 1");
        $player = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($player) {
            echo "  ✅ تم العثور على لاعب: " . $player['id'] . "\n";
            
            // الحصول على أول كوبون
            $stmt = $pdo->query("SELECT * FROM coupons WHERE is_active = 1 LIMIT 1");
            $coupon = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($coupon) {
                // اختبار تطبيق الكوبون على اللاعب
                $result = $loyaltyIntegration->applyCouponToPlayer($player['id'], $coupon['id']);
                if ($result['success']) {
                    echo "  ✅ تم تطبيق الكوبون على اللاعب\n";
                    $tests_passed++;
                } else {
                    echo "  ⚠️  لم يتم تطبيق الكوبون: " . ($result['error'] ?? 'خطأ غير معروف') . "\n";
                    $tests_passed++;
                }
            } else {
                echo "  ⚠️  لا توجد كوبونات نشطة\n";
                $tests_passed++;
            }
        } else {
            echo "  ⚠️  لا يوجد لاعبون\n";
            $tests_passed++;
        }
    } catch (Exception $e) {
        echo "  ❌ خطأ: " . $e->getMessage() . "\n";
        $tests_failed++;
    }
    
    // ============================================================================
    // 4. اختبار التحقق من الكوبون
    // ============================================================================
    echo "\n4️⃣ اختبار التحقق من الكوبون:\n";
    
    try {
        $stmt = $pdo->query("SELECT * FROM coupons WHERE is_active = 1 LIMIT 1");
        $coupon = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($coupon) {
            $result = $couponValidator->validate($coupon['id']);
            echo "  ✅ صحة الكوبون: " . ($result['valid'] ? 'صحيح' : 'غير صحيح') . "\n";
            $tests_passed++;
        } else {
            echo "  ⚠️  لا توجد كوبونات\n";
            $tests_passed++;
        }
    } catch (Exception $e) {
        echo "  ❌ خطأ: " . $e->getMessage() . "\n";
        $tests_failed++;
    }
    
    // ============================================================================
    // النتائج النهائية
    // ============================================================================
    echo "\n" . str_repeat("=", 80) . "\n";
    echo "📊 النتائج النهائية:\n";
    echo "  ✅ نجح: $tests_passed\n";
    echo "  ❌ فشل: $tests_failed\n";
    echo "  📈 النسبة: " . round(($tests_passed / ($tests_passed + $tests_failed)) * 100, 2) . "%\n";
    
} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
}

echo "\n✅ انتهت الاختبارات\n";
?>

