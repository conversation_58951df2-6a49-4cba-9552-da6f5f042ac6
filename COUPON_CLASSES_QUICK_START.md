# دليل البدء السريع لفئات الكوبونات
# Coupon Classes Quick Start Guide

---

## 🚀 البدء السريع

### 1. استيراد الفئات

```php
<?php
require_once 'includes/CouponManager.php';
require_once 'includes/CouponValidator.php';
require_once 'includes/CouponPaymentIntegration.php';
require_once 'includes/CouponSubscriptionIntegration.php';
require_once 'includes/CouponLoyaltyIntegration.php';
require_once 'includes/CouponReportingSystem.php';
```

### 2. إنشاء الكائنات

```php
$couponManager = new CouponManager($pdo);
$couponValidator = new CouponValidator($pdo, $couponManager);
$paymentIntegration = new CouponPaymentIntegration($pdo, $couponManager, $couponValidator);
$subscriptionIntegration = new CouponSubscriptionIntegration($pdo, $couponManager, $couponValidator);
$loyaltyIntegration = new CouponLoyaltyIntegration($pdo, $couponManager);
$reportingSystem = new CouponReportingSystem($pdo);
```

---

## 📝 أمثلة الاستخدام

### إنشاء كوبون

```php
$result = $couponManager->createCoupon([
    'code' => 'SUMMER20',
    'name' => 'خصم الصيف',
    'type' => 'percentage',
    'value' => 20,
    'minimum_amount' => 100,
    'usage_limit' => 100,
    'usage_limit_per_user' => 1,
    'valid_from' => '2025-06-01',
    'valid_until' => '2025-08-31',
    'is_active' => true,
    'created_by' => 1
]);
```

### التحقق من الكوبون

```php
$validation = $couponValidator->validate('SUMMER20', [
    'amount' => 500,
    'user_id' => 123,
    'plan_id' => 1
]);

if ($validation['valid']) {
    echo "الكوبون صحيح";
} else {
    echo "خطأ: " . $validation['error'];
}
```

### معالجة الدفع مع الكوبون

```php
$result = $paymentIntegration->processPaymentWithCoupon([
    'amount' => 500,
    'user_id' => 123,
    'coupon_code' => 'SUMMER20',
    'plan_id' => 1
]);

if ($result['success']) {
    echo "المبلغ النهائي: " . $result['final_amount'];
}
```

### تطبيق على الاشتراك

```php
$result = $subscriptionIntegration->applyToNewSubscription([
    'plan_id' => 1,
    'user_id' => 123,
    'player_id' => 456,
    'coupon_code' => 'SUMMER20'
]);

if ($result['success']) {
    echo "تم إنشاء الاشتراك برقم: " . $result['subscription_id'];
}
```

### منح نقاط الولاء

```php
$result = $loyaltyIntegration->awardLoyaltyPoints(
    couponId: 1,
    userId: 123,
    discountAmount: 100
);

if ($result['success']) {
    echo "تم منح " . $result['points_awarded'] . " نقطة";
}
```

### الحصول على التقارير

```php
// تقرير الاستخدام
$usageReport = $reportingSystem->getUsageReport([
    'start_date' => '2025-01-01',
    'end_date' => '2025-12-31'
]);

// أفضل الكوبونات
$topCoupons = $reportingSystem->getTopCouponsReport(10);

// تقرير الإيرادات
$revenueReport = $reportingSystem->getRevenueReport([
    'start_date' => '2025-01-01',
    'end_date' => '2025-12-31'
]);
```

---

## 🔍 معالجة الأخطاء

```php
$result = $couponManager->createCoupon($data);

if (!$result['success']) {
    error_log("خطأ: " . $result['error']);
    // معالجة الخطأ
} else {
    echo "تم الإنشاء برقم: " . $result['id'];
}
```

---

## 📊 هيكل الاستجابة

### النجاح
```php
[
    'success' => true,
    'id' => 1,
    'message' => 'تم بنجاح'
]
```

### الفشل
```php
[
    'success' => false,
    'error' => 'رسالة الخطأ'
]
```

---

## 🔐 معايير الأمان

- ✅ استخدام Prepared Statements
- ✅ التحقق من الصلاحيات
- ✅ سجل تدقيق شامل
- ✅ معالجة الأخطاء الآمنة

---

## 📚 المراجع

| الملف | الوصف |
|------|-------|
| `includes/CouponManager.php` | إدارة الكوبونات |
| `includes/CouponValidator.php` | التحقق |
| `includes/CouponPaymentIntegration.php` | تكامل الدفع |
| `includes/CouponSubscriptionIntegration.php` | تكامل الاشتراكات |
| `includes/CouponLoyaltyIntegration.php` | تكامل الولاء |
| `includes/CouponReportingSystem.php` | التقارير |

---

## ❓ الأسئلة الشائعة

**س: كيف أتحقق من صحة الكوبون؟**
ج: استخدم `$couponValidator->validate()`

**س: كيف أحسب الخصم؟**
ج: استخدم `$couponManager->calculateDiscount()`

**س: كيف أطبق الكوبون على الدفعة؟**
ج: استخدم `$paymentIntegration->applyToPayment()`

**س: كيف أحصل على التقارير؟**
ج: استخدم `$reportingSystem->getUsageReport()`

---

**آخر تحديث:** 2025-12-28

