<?php
/**
 * تكامل الكوبونات مع نظام الدفع
 * Coupon Payment Integration
 */

class CouponPaymentIntegration {
    private $pdo;
    private $couponManager;
    private $couponValidator;
    
    public function __construct(PDO $pdo, CouponManager $couponManager, CouponValidator $couponValidator) {
        $this->pdo = $pdo;
        $this->couponManager = $couponManager;
        $this->couponValidator = $couponValidator;
    }
    
    /**
     * معالجة الدفع مع الكوبون
     */
    public function processPaymentWithCoupon(array $paymentData): array {
        try {
            $couponCode = $paymentData['coupon_code'] ?? null;
            $originalAmount = (float)$paymentData['amount'];
            $userId = (int)$paymentData['user_id'];
            $planId = (int)($paymentData['plan_id'] ?? 0);
            
            $finalAmount = $originalAmount;
            $discountAmount = 0;
            $couponId = null;
            
            // التحقق من الكوبون إن وجد
            if ($couponCode) {
                $context = [
                    'amount' => $originalAmount,
                    'user_id' => $userId
                ];
                
                if ($planId) {
                    $context['plan_id'] = $planId;
                }
                
                $validation = $this->couponValidator->validate($couponCode, $context);
                
                if (!$validation['valid']) {
                    return [
                        'success' => false,
                        'error' => $validation['error'],
                        'original_amount' => $originalAmount,
                        'final_amount' => $originalAmount
                    ];
                }
                
                $coupon = $validation['coupon'];
                $couponId = $coupon['id'];
                
                // حساب الخصم
                $discountAmount = $this->couponManager->calculateDiscount($coupon, $originalAmount);
                $finalAmount = $originalAmount - $discountAmount;
            }
            
            return [
                'success' => true,
                'original_amount' => $originalAmount,
                'discount_amount' => $discountAmount,
                'final_amount' => $finalAmount,
                'coupon_id' => $couponId,
                'message' => 'تم حساب المبلغ بنجاح'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'original_amount' => $paymentData['amount'] ?? 0,
                'final_amount' => $paymentData['amount'] ?? 0
            ];
        }
    }
    
    /**
     * تطبيق الكوبون على الفاتورة
     */
    public function applyToInvoice(int $invoiceId, string $couponCode, int $userId): array {
        try {
            // الحصول على بيانات الفاتورة
            $invoice = $this->getInvoice($invoiceId);
            if (!$invoice) {
                return ['success' => false, 'error' => 'الفاتورة غير موجودة'];
            }
            
            // التحقق من الكوبون
            $context = [
                'amount' => (float)$invoice['total_amount'],
                'user_id' => $userId
            ];
            
            $validation = $this->couponValidator->validate($couponCode, $context);
            if (!$validation['valid']) {
                return ['success' => false, 'error' => $validation['error']];
            }
            
            $coupon = $validation['coupon'];
            $discountAmount = $this->couponManager->calculateDiscount($coupon, (float)$invoice['total_amount']);
            $finalAmount = (float)$invoice['total_amount'] - $discountAmount;
            
            // تحديث الفاتورة
            $sql = "UPDATE invoices SET 
                    coupon_id = ?, 
                    discount_amount = ?, 
                    final_amount = ?,
                    updated_at = NOW()
                    WHERE id = ?";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$coupon['id'], $discountAmount, $finalAmount, $invoiceId]);
            
            // تطبيق الكوبون
            $this->couponManager->applyCoupon(
                $coupon['id'],
                $userId,
                (float)$invoice['total_amount'],
                null,
                null
            );
            
            return [
                'success' => true,
                'discount_amount' => $discountAmount,
                'final_amount' => $finalAmount,
                'message' => 'تم تطبيق الكوبون على الفاتورة'
            ];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * تطبيق الكوبون على الدفعة
     */
    public function applyToPayment(int $paymentId, string $couponCode, int $userId): array {
        try {
            // الحصول على بيانات الدفعة
            $payment = $this->getPayment($paymentId);
            if (!$payment) {
                return ['success' => false, 'error' => 'الدفعة غير موجودة'];
            }
            
            // التحقق من الكوبون
            $context = [
                'amount' => (float)$payment['amount'],
                'user_id' => $userId
            ];
            
            $validation = $this->couponValidator->validate($couponCode, $context);
            if (!$validation['valid']) {
                return ['success' => false, 'error' => $validation['error']];
            }
            
            $coupon = $validation['coupon'];
            $discountAmount = $this->couponManager->calculateDiscount($coupon, (float)$payment['amount']);
            $finalAmount = (float)$payment['amount'] - $discountAmount;
            
            // تحديث الدفعة
            $sql = "UPDATE payments SET 
                    coupon_id = ?, 
                    discount_amount = ?, 
                    final_amount = ?,
                    updated_at = NOW()
                    WHERE id = ?";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$coupon['id'], $discountAmount, $finalAmount, $paymentId]);
            
            // تطبيق الكوبون
            $this->couponManager->applyCoupon(
                $coupon['id'],
                $userId,
                (float)$payment['amount'],
                null,
                $paymentId
            );
            
            return [
                'success' => true,
                'discount_amount' => $discountAmount,
                'final_amount' => $finalAmount,
                'message' => 'تم تطبيق الكوبون على الدفعة'
            ];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * الحصول على بيانات الفاتورة
     */
    private function getInvoice(int $invoiceId): ?array {
        $sql = "SELECT * FROM invoices WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$invoiceId]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }
    
    /**
     * الحصول على بيانات الدفعة
     */
    private function getPayment(int $paymentId): ?array {
        $sql = "SELECT * FROM payments WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$paymentId]);
        return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
    }
    
    /**
     * إلغاء الكوبون من الدفعة
     */
    public function removeCouponFromPayment(int $paymentId): array {
        try {
            $payment = $this->getPayment($paymentId);
            if (!$payment) {
                return ['success' => false, 'error' => 'الدفعة غير موجودة'];
            }
            
            // تحديث الدفعة
            $sql = "UPDATE payments SET 
                    coupon_id = NULL, 
                    discount_amount = 0, 
                    final_amount = amount,
                    updated_at = NOW()
                    WHERE id = ?";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$paymentId]);
            
            // تحديث سجل الاستخدام
            if ($payment['coupon_id']) {
                $this->pdo->prepare(
                    "UPDATE coupon_usage SET status = 'cancelled' WHERE payment_id = ?"
                )->execute([$paymentId]);
            }
            
            return ['success' => true, 'message' => 'تم إلغاء الكوبون'];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
}
?>

