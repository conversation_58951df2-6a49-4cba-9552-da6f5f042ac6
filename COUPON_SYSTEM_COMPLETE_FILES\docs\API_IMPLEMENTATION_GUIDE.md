# 📡 دليل تطوير واجهات API
# API Development Guide

## 🎯 الهدف | Objective

تطوير واجهات API شاملة لتكامل الكوبونات مع نظام الاشتراكات.

---

## 📋 المحتويات | Contents

1. [الملفات المُنشأة](#الملفات-المُنشأة)
2. [نقاط النهاية](#نقاط-النهاية)
3. [أمثلة الاستخدام](#أمثلة-الاستخدام)
4. [معالجة الأخطاء](#معالجة-الأخطاء)
5. [الاختبار](#الاختبار)

---

## 📁 الملفات المُنشأة | Created Files

### 1. api/subscription_coupon_api.php
**الوصف:** واجهة API رئيسية لتكامل الكوبونات مع الاشتراكات
**الحجم:** 150 سطر
**الميزات:**
- ✅ 8 نقاط نهاية رئيسية
- ✅ معالجة شاملة للأخطاء
- ✅ التحقق من المصادقة
- ✅ استجابات JSON موحدة

### 2. docs/API_ENDPOINTS_GUIDE.md
**الوصف:** دليل شامل لنقاط النهاية
**المحتوى:**
- ✅ توثيق 8 نقاط نهاية
- ✅ أمثلة الاستخدام
- ✅ رموز الحالة
- ✅ متطلبات المصادقة

### 3. tests/test_subscription_coupon_api.php
**الوصف:** مجموعة اختبارات شاملة
**الاختبارات:**
- ✅ اختبار حساب السعر
- ✅ اختبار التحقق من الكوبون
- ✅ اختبار الحصول على الاشتراكات
- ✅ اختبار الخطط المتاحة
- ✅ اختبار الكوبونات النشطة

### 4. docs/API_IMPLEMENTATION_GUIDE.md
**الوصف:** دليل التطوير والتنفيذ
**المحتوى:**
- ✅ خطوات التطوير
- ✅ أمثلة الكود
- ✅ أفضل الممارسات
- ✅ استكشاف الأخطاء

---

## 🔗 نقاط النهاية | Endpoints

### 1. حساب السعر مع الكوبون
```
POST api/subscription_coupon_api.php?action=calculate_price
```
**المتطلبات:** plan_id, coupon_code (optional)
**الاستجابة:** base_price, coupon_discount, final_price

### 2. إنشاء اشتراك جديد مع الكوبون
```
POST api/subscription_coupon_api.php?action=create_with_coupon
```
**المتطلبات:** plan_id, player_id, coupon_code (optional)
**الاستجابة:** subscription_id, subscription data

### 3. تجديد الاشتراك مع الكوبون
```
POST api/subscription_coupon_api.php?action=renew_with_coupon
```
**المتطلبات:** subscription_id, coupon_code (optional)
**الاستجابة:** subscription_id, subscription data

### 4. ترقية الاشتراك مع الكوبون
```
POST api/subscription_coupon_api.php?action=upgrade_with_coupon
```
**المتطلبات:** subscription_id, new_plan_id, coupon_code (optional)
**الاستجابة:** subscription_id, subscription data

### 5. إزالة الكوبون من الاشتراك
```
POST api/subscription_coupon_api.php?action=remove_coupon
```
**المتطلبات:** subscription_id
**الاستجابة:** subscription data

### 6. التحقق من صحة الكوبون
```
POST api/subscription_coupon_api.php?action=validate_coupon
```
**المتطلبات:** coupon_code, plan_id
**الاستجابة:** valid, coupon data

### 7. الحصول على بيانات الاشتراك
```
POST api/subscription_coupon_api.php?action=get_subscription
```
**المتطلبات:** subscription_id
**الاستجابة:** subscription data

### 8. الحصول على قائمة الاشتراكات
```
POST api/subscription_coupon_api.php?action=list_subscriptions
```
**المتطلبات:** player_id
**الاستجابة:** subscriptions array, count

---

## 💻 أمثلة الاستخدام | Usage Examples

### مثال 1: حساب السعر
```php
$data = [
    'plan_id' => 1,
    'coupon_code' => 'SAVE10'
];

$response = callAPI('calculate_price', $data);
echo "السعر النهائي: " . $response['final_price'];
```

### مثال 2: إنشاء اشتراك
```php
$data = [
    'plan_id' => 1,
    'player_id' => 123,
    'coupon_code' => 'SAVE10'
];

$response = callAPI('create_with_coupon', $data);
echo "معرف الاشتراك: " . $response['subscription_id'];
```

### مثال 3: تجديد الاشتراك
```php
$data = [
    'subscription_id' => 456,
    'coupon_code' => 'RENEW20'
];

$response = callAPI('renew_with_coupon', $data);
echo "تم التجديد بنجاح";
```

---

## ⚠️ معالجة الأخطاء | Error Handling

### أنواع الأخطاء
1. **خطأ المصادقة** - غير مصرح
2. **خطأ البيانات** - بيانات ناقصة
3. **خطأ الكوبون** - كوبون غير صحيح
4. **خطأ الخادم** - خطأ في قاعدة البيانات

### معالجة الأخطاء
```php
try {
    $response = callAPI('action', $data);
    if ($response['success']) {
        // نجح
    } else {
        // فشل
        echo $response['error'];
    }
} catch (Exception $e) {
    // خطأ
    echo $e->getMessage();
}
```

---

## 🧪 الاختبار | Testing

### تشغيل الاختبارات
```bash
php tests/test_subscription_coupon_api.php
```

### الاختبارات المتضمنة
1. ✅ اختبار حساب السعر
2. ✅ اختبار التحقق من الكوبون
3. ✅ اختبار الحصول على الاشتراكات
4. ✅ اختبار الخطط المتاحة
5. ✅ اختبار الكوبونات النشطة

---

## 🔐 الأمان | Security

### متطلبات الأمان
1. ✅ التحقق من المصادقة
2. ✅ التحقق من صحة البيانات
3. ✅ معالجة الأخطاء الآمنة
4. ✅ تسجيل العمليات

---

## 📊 الإحصائيات | Statistics

| المقياس | القيمة |
|--------|--------|
| عدد نقاط النهاية | 8 |
| عدد الملفات المُنشأة | 4 |
| عدد الاختبارات | 5 |
| عدد أمثلة الاستخدام | 3 |

---

**تاريخ الإنشاء:** 2025-12-28
**الإصدار:** 1.0
**الحالة:** ✅ جاهز للاستخدام

