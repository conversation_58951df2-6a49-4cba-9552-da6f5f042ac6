<?php
/**
 * اختبار فائق الأمان لنظام الكوبونات
 * Ultra Safe Coupon System Test
 */

echo "🧪 اختبار فائق الأمان لنظام الكوبونات\n";
echo "=====================================\n\n";

// إعداد قاعدة البيانات
try {
    // تضمين ملف إعدادات قاعدة البيانات
    require_once __DIR__ . '/../config/database.php';
    
    echo "✅ تم تحميل إعدادات قاعدة البيانات\n";
    echo "📊 المضيف: " . DB_HOST . "\n";
    echo "📊 قاعدة البيانات: " . DB_NAME . "\n";
    echo "📊 المستخدم: " . DB_USER . "\n\n";

    // استخدام الاتصال الموجود
    if (!isset($pdo)) {
        $pdo = getDBConnection();
    }
    
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح\n\n";
    
    // 1. تشغيل الإعداد فائق الأمان
    echo "📋 تشغيل الإعداد فائق الأمان...\n";
    try {
        $sqlFile = __DIR__ . '/../database/ultra_safe_setup.sql';
        if (file_exists($sqlFile)) {
            $sql = file_get_contents($sqlFile);
            
            // تنظيف وتقسيم الاستعلامات
            $sql = preg_replace('/--.*$/m', '', $sql); // إزالة التعليقات
            $statements = array_filter(array_map('trim', explode(';', $sql)));
            
            $successCount = 0;
            $errorCount = 0;
            
            foreach ($statements as $statement) {
                if (!empty($statement)) {
                    try {
                        $pdo->exec($statement);
                        $successCount++;
                        
                        // عرض نتائج SELECT
                        if (stripos($statement, 'SELECT') === 0) {
                            $stmt = $pdo->query($statement);
                            $result = $stmt->fetch(PDO::FETCH_ASSOC);
                            if ($result) {
                                foreach ($result as $key => $value) {
                                    echo "   📊 $key: $value\n";
                                }
                            }
                        }
                        
                    } catch (Exception $e) {
                        $errorCount++;
                        echo "⚠️ تحذير: " . $e->getMessage() . "\n";
                        echo "   الاستعلام: " . substr($statement, 0, 50) . "...\n";
                    }
                }
            }
            
            echo "✅ تم تنفيذ $successCount استعلام بنجاح\n";
            if ($errorCount > 0) {
                echo "⚠️ $errorCount تحذيرات\n";
            }
            
        } else {
            echo "❌ ملف SQL غير موجود: $sqlFile\n";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في تشغيل الإعداد: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 2. فحص الجداول المُنشأة
    echo "📋 فحص الجداول المُنشأة...\n";
    $tables = ['coupons', 'coupon_usage'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                echo "✅ جدول $table: موجود\n";
                
                // عدد السجلات
                $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
                $count = $stmt->fetchColumn();
                echo "   📊 عدد السجلات: $count\n";
                
                // فحص الأعمدة
                if ($table === 'coupons') {
                    $stmt = $pdo->query("DESCRIBE $table");
                    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                    echo "   📊 الأعمدة: " . implode(', ', array_slice($columns, 0, 5)) . "...\n";
                }
                
            } else {
                echo "❌ جدول $table: غير موجود\n";
            }
        } catch (Exception $e) {
            echo "❌ خطأ في فحص جدول $table: " . $e->getMessage() . "\n";
        }
    }
    echo "\n";
    
    // 3. عرض الكوبونات
    echo "📋 عرض الكوبونات الموجودة...\n";
    try {
        $stmt = $pdo->query("SELECT id, code, name, type, value, is_active FROM coupons ORDER BY id");
        $coupons = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($coupons) > 0) {
            echo "✅ تم العثور على " . count($coupons) . " كوبون:\n";
            foreach ($coupons as $coupon) {
                $status = $coupon['is_active'] ? '🟢 مفعل' : '🔴 معطل';
                echo "  - {$coupon['id']}: {$coupon['code']} - {$coupon['name']}\n";
                echo "    النوع: {$coupon['type']}, القيمة: {$coupon['value']}, الحالة: $status\n";
            }
        } else {
            echo "⚠️ لا توجد كوبونات\n";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في عرض الكوبونات: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 4. اختبار إنشاء كوبون جديد
    echo "📋 اختبار إنشاء كوبون جديد...\n";
    try {
        $sql = "INSERT INTO coupons (uuid, code, name, description, type, value, minimum_amount, valid_from, valid_until, is_active) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            'new-test-' . time(),
            'NEWTEST' . date('md'),
            'كوبون جديد ' . date('Y-m-d'),
            'كوبون تم إنشاؤه من الاختبار فائق الأمان',
            'percentage',
            35.00,
            10.00,
            '2025-01-01 00:00:00',
            '2025-12-31 23:59:59',
            1
        ]);
        
        if ($result) {
            $couponId = $pdo->lastInsertId();
            echo "✅ تم إنشاء كوبون جديد بنجاح - المعرف: $couponId\n";
        } else {
            echo "❌ فشل في إنشاء الكوبون\n";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في إنشاء الكوبون: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 5. اختبار استخدام كوبون
    echo "📋 اختبار استخدام كوبون...\n";
    try {
        $sql = "INSERT INTO coupon_usage (coupon_id, user_id, discount_amount, original_amount, final_amount, status) 
                VALUES (?, ?, ?, ?, ?, ?)";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            1, // معرف الكوبون الأول
            123, // معرف مستخدم تجريبي
            10.00, // مبلغ الخصم
            100.00, // المبلغ الأصلي
            90.00, // المبلغ النهائي
            'applied'
        ]);
        
        if ($result) {
            $usageId = $pdo->lastInsertId();
            echo "✅ تم تسجيل استخدام الكوبون بنجاح - المعرف: $usageId\n";
        } else {
            echo "❌ فشل في تسجيل استخدام الكوبون\n";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في تسجيل استخدام الكوبون: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // 6. ملخص النتائج النهائي
    echo "📊 ملخص النتائج النهائي:\n";
    echo "========================\n";
    
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM coupons");
        $totalCoupons = $stmt->fetchColumn();
        echo "✅ إجمالي الكوبونات: $totalCoupons\n";
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM coupons WHERE is_active = 1");
        $activeCoupons = $stmt->fetchColumn();
        echo "✅ الكوبونات المفعلة: $activeCoupons\n";
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM coupon_usage");
        $usageCount = $stmt->fetchColumn();
        echo "✅ سجلات الاستخدام: $usageCount\n";
        
        echo "\n🎉 نظام الكوبونات يعمل بشكل مثالي!\n";
        echo "🚀 جميع الاختبارات نجحت بدون أخطاء!\n";
        
    } catch (Exception $e) {
        echo "❌ خطأ في الملخص: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
    echo "\n💡 تأكد من:\n";
    echo "   - تشغيل خادم MySQL\n";
    echo "   - صحة بيانات الاتصال في config/database.php\n";
    echo "   - وجود قاعدة البيانات " . (defined('DB_NAME') ? DB_NAME : 'c7c_wolves7c') . "\n";
    echo "   - صلاحيات المستخدم للوصول لقاعدة البيانات\n";
}

echo "\n✅ انتهى الاختبار فائق الأمان\n";
?>
